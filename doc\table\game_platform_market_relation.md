## 表: game_platform_market_relation

```sql
CREATE TABLE `game_platform_market_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_platform_id` bigint(20) unsigned NOT NULL,
  `game_market_id` bigint(20) unsigned NOT NULL,
  `delsign` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_platform_id`,`game_market_id`) USING BTREE,
  KEY `idx_tag_id` (`game_market_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2620 DEFAULT CHARSET=utf8mb4 COMMENT='爬取游戏地区关联表'
```

