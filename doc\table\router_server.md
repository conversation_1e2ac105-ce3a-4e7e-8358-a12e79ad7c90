## 表: router_server

```sql
CREATE TABLE `router_server` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ecs_object_id` varchar(64) DEFAULT NULL COMMENT '创建服务器的项目id',
  `ecs_job_id` varchar(64) DEFAULT NULL COMMENT '创建服务器的任务id',
  `ecs_order_id` varchar(64) DEFAULT NULL COMMENT '开启云服务的订单id',
  `ecs_server_id` varchar(64) DEFAULT NULL COMMENT '云服务器id(华为云id)',
  `domain` varchar(64) DEFAULT NULL COMMENT '服务器域名,域名目前没有不用存',
  `public_ip` varchar(64) DEFAULT NULL COMMENT '前端节点 公网ip，暂时业务用不到可以不存',
  `inner_ip` varchar(64) DEFAULT NULL COMMENT '前端节点 内网ip ',
  `vpn_port` int(11) DEFAULT NULL COMMENT 'vpn服务端口',
  `server_region` int(11) DEFAULT NULL COMMENT '前端节点 1.香港节点\n2.广州节点\n3.国内其他节点\n4.海外其他节点',
  `server_name` varchar(64) DEFAULT NULL COMMENT '名称，线路id+小写字母(region)+ip后缀',
  `ecs_create_info` json DEFAULT NULL COMMENT '存入开启esc的请求信息',
  `ecs_server_info` json DEFAULT NULL COMMENT 'esc服务器信息',
  `liner_config_id` int(11) DEFAULT NULL COMMENT '线路配置ID',
  `ecs_config_id` int(11) DEFAULT NULL COMMENT '服务器配置ID',
  `last_heartbeat_time` timestamp NULL DEFAULT NULL COMMENT '最后上报时间',
  `connection_num` int(11) NOT NULL DEFAULT '0' COMMENT '连接数',
  `connection_num_threshold` int(11) NOT NULL DEFAULT '5000' COMMENT '连接数阈值',
  `cpu_use` double(11,2) NOT NULL DEFAULT '0.00' COMMENT '当前cpu占用',
  `cpu_threshold` double(11,2) NOT NULL DEFAULT '70.00' COMMENT 'cpu报警阈值 70',
  `ping_delay_ms` decimal(10,4) NOT NULL DEFAULT '9999.0000' COMMENT 'ping值',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '0.游戏 1.其他',
  `ecs_type` int(11) DEFAULT NULL COMMENT '1-华为 2-阿里云',
  `is_foreign` tinyint(4) DEFAULT NULL COMMENT '0-国内 1-国外',
  `status` int(11) NOT NULL COMMENT '0.异常 1.正常 2.正在创建 3.正在初始化 4.待关闭 5.已退 6待升级 7升级中',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 正常 1: 删除',
  `image_version` decimal(16,4) DEFAULT NULL COMMENT '镜像版本号',
  `charging_mode` varchar(50) NOT NULL COMMENT '华为：prePaid周期付款，postPaid按需付款。 阿里：PostPaid按需付费， PrePaid包年包月',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=149 DEFAULT CHARSET=utf8mb4 COMMENT='esc云服务器表'
```

