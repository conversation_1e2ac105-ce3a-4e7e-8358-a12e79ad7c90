## 表: announcement_template

```sql
CREATE TABLE `announcement_template` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_list` text NOT NULL COMMENT '用户id以逗号分割，0表示全部用户',
  `title` varchar(100) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `publish_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '发布时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COMMENT='公告表'
```

