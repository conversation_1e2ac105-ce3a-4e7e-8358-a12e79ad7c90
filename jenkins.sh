echo "Restart 参数为: $Restart"

if [ "$Restart" = "TEST-AL" ]
then
  servers=("113.45.76.93" "139.9.205.217" "106.14.72.75" "8.153.201.206" "47.117.75.191" "8.152.207.166" "172.25.221.90" "101.44.65.202" "124.243.169.76" "47.88.12.20" "47.80.7.16" "8.211.166.136" "47.254.152.30")
  for server in "${servers[@]}"
  do
    echo "重启节点: $server"
    bash /root/fastbird-dns/linux/push.sh "$server"
  done
else
  echo "只重启指定节点: $Restart"
  bash /root/fastbird-dns/linux/push.sh "$Restart"
fi