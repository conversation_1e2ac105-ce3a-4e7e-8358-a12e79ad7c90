## 表: router_server_liner_server_relation

```sql
CREATE TABLE `router_server_liner_server_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `liner_ecs_config_relation_id` int(11) DEFAULT NULL COMMENT '线路配置关联表ID',
  `liner_config_id` int(11) DEFAULT NULL COMMENT '线路ID，冗余字段',
  `server_id` int(11) DEFAULT NULL COMMENT 'router_server表服务器实例ID',
  `last_ip` int(11) DEFAULT NULL COMMENT 'IP 地址的最后一段, 相同ilner 相同last_id 是一组线路服务器实例',
  `status` int(11) NOT NULL COMMENT '0.异常 1.正常 2.正在创建 3.正在初始化 4.待关闭 5.已退 6.待升级 7.升级中',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 正常 1: 删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=107 DEFAULT CHARSET=utf8mb4 COMMENT='节点线路表'
```

