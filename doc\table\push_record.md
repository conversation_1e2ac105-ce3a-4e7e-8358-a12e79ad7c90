## 表: push_record

```sql
CREATE TABLE `push_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `push_channel` tinyint(4) NOT NULL COMMENT '推送渠道：-1未区分 0-Apns(苹果) 1-FCM 2-华为 3-小米',
  `system_channel` tinyint(4) NOT NULL COMMENT '设备平台：1-苹果 2-安卓 3-PC 4-鸿蒙',
  `push_id` bigint(20) NOT NULL COMMENT '关联的推送任务ID',
  `account_list` text COMMENT '实际发送的用户账号列表(逗号分隔)',
  `push_result` tinyint(4) NOT NULL COMMENT '推送结果：1-成功 2-失败',
  `fail_result` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_push_id` (`push_id`),
  KEY `idx_push_channel` (`push_channel`),
  KEY `idx_system_channel` (`system_channel`),
  KEY `idx_push_result` (`push_result`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COMMENT='推送发送记录表'
```

