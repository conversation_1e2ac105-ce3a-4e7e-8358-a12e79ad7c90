## 表: router_xray_inbound_shadowsocks_extend

```sql
CREATE TABLE `router_xray_inbound_shadowsocks_extend` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inbound_id` bigint(20) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL COMMENT 'id',
  `encry_method` varchar(255) DEFAULT NULL,
  `nonce` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inboundid_idx` (`inbound_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4
```

