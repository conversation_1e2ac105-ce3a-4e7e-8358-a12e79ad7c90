## 表: vpn_connection_apply_record

```sql
CREATE TABLE `vpn_connection_apply_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `device_uuid` varchar(255) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL COMMENT '加速游戏id',
  `game_region_id` bigint(20) DEFAULT NULL COMMENT '加速游戏服id',
  `account_pay_status` int(11) DEFAULT NULL COMMENT '用户支付状态 1-free用户 2-订阅用户',
  `connection_type` int(11) DEFAULT NULL COMMENT '连接业务类型 1-游戏 2-。。',
  `server_id` int(11) DEFAULT NULL,
  `vpn_u` varchar(255) DEFAULT NULL,
  `vpn_s` varchar(255) DEFAULT NULL,
  `vpn_us` varchar(255) DEFAULT NULL,
  `vpn_p` varchar(255) DEFAULT NULL,
  `fail_reason` varchar(1024) DEFAULT NULL COMMENT '连接失败原因',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `origin_ip` varchar(255) DEFAULT NULL,
  `country_code` varchar(255) DEFAULT NULL,
  `system_channel` int(11) DEFAULT NULL,
  `account_membership_type` int(11) DEFAULT NULL,
  `origin_game_region_id` bigint(20) DEFAULT NULL COMMENT '原始访问的gameRegionId',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6148 DEFAULT CHARSET=utf8mb4
```

