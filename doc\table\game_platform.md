## 表: game_platform

```sql
CREATE TABLE `game_platform` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '游戏名称',
  `image_url` varchar(255) NOT NULL COMMENT '游戏图片',
  `is_foreign` tinyint(4) NOT NULL COMMENT '0.国内ip 1.国外ip',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，降序',
  `is_default` int(11) NOT NULL DEFAULT '0' COMMENT '0. 非默认，1. 默认',
  `delay` int(11) NOT NULL DEFAULT '0' COMMENT '网络延迟',
  `mark` varchar(500) DEFAULT NULL COMMENT '游戏简介',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `bundle_id` varchar(100) NOT NULL COMMENT '平台唯一性包名',
  `system_channel` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1ios 2android 3pc 4鸿蒙',
  `game_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'game表主键，默认0表示未关联',
  `delsign` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0. 存在 1.删除',
  `apple_id` bigint(20) DEFAULT NULL COMMENT 'Appstore平台的id，安卓平台没有该字段',
  `developer` varchar(200) DEFAULT NULL,
  `developerUrl` varchar(200) DEFAULT NULL,
  `developerId` varchar(200) DEFAULT NULL,
  `genre` varchar(200) DEFAULT NULL,
  `genreId` varchar(200) DEFAULT NULL,
  `market_app_id` varchar(100) DEFAULT NULL COMMENT '应用市场App_id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `bundle_system_id` (`bundle_id`,`system_channel`) USING BTREE,
  KEY `idx_created_at` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=92250 DEFAULT CHARSET=utf8mb4 COMMENT='爬取游戏（区分平台）信息表'
```

