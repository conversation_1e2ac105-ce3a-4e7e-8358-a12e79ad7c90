## 表: redemption_code_use_record

```sql
CREATE TABLE `redemption_code_use_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `redemption_code_id` bigint(20) NOT NULL,
  `use_account_id` bigint(20) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `redemption_code_id` (`redemption_code_id`,`use_account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COMMENT='兑换码使用记录表'
```

