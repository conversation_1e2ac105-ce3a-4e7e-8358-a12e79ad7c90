package tool

import (
	"fastbird-dns/infra/logger"
	"os"
	"runtime"
	"time"
)

// GetSystemInfo 获取系统信息
func GetSystemInfo() map[string]interface{} {
	hostname, err := os.Hostname()
	if err != nil {
		logger.Error("Failed to get hostname:", err)
		hostname = "unknown"
	}

	// 获取内存信息
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	return map[string]interface{}{
		"hostname":     hostname,
		"os":           runtime.GOOS,
		"arch":         runtime.GOARCH,
		"go_version":   runtime.Version(),
		"cpu_num":      runtime.NumCPU(),
		"goroutines":   runtime.NumGoroutine(),
		"memory_alloc": memStats.Alloc,
		"memory_sys":   memStats.Sys,
		"uptime":       time.Now().Unix(),
	}
}

// GetProcessInfo 获取进程信息
func GetProcessInfo() map[string]interface{} {
	pid := os.Getpid()
	ppid := os.Getppid()
	wd, err := os.Getwd()
	if err != nil {
		logger.Error("Failed to get working directory:", err)
		wd = "unknown"
	}

	return map[string]interface{}{
		"pid":              pid,
		"parent_pid":       ppid,
		"working_dir":      wd,
		"executable_path":  os.Args[0],
		"command_line":     os.Args,
		"num_goroutines":   runtime.NumGoroutine(),
		"go_max_procs":     runtime.GOMAXPROCS(0),
	}
} 