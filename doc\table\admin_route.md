## 表: admin_route

```sql
CREATE TABLE `admin_route` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `path` varchar(255) NOT NULL COMMENT '路由',
  `name` varchar(255) DEFAULT NULL COMMENT '排序',
  `sort` int(11) DEFAULT NULL,
  `group_id` int(11) DEFAULT '1' COMMENT '所属的组id',
  `mark` varchar(255) DEFAULT NULL COMMENT '描述',
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  `level` int(11) DEFAULT '1' COMMENT '路由级别',
  `front_path` varchar(255) NOT NULL COMMENT '前端路由',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4
```

