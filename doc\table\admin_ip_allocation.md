## 表: admin_ip_allocation

```sql
CREATE TABLE `admin_ip_allocation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ecs_config_relation_id` int(11) DEFAULT NULL COMMENT 'ecs 配置对 id',
  `ip_last` int(11) NOT NULL COMMENT 'IP 地址的最后一段',
  `is_using` tinyint(4) DEFAULT '0' COMMENT '0 表示未占用，1 表示占用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_ip_last` (`ip_last`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=255 DEFAULT CHARSET=utf8mb4
```

