## 表: router_server_relation

```sql
CREATE TABLE `router_server_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server_id_from` int(11) DEFAULT NULL,
  `public_ip_from` varchar(255) DEFAULT NULL COMMENT '前端节点 公网ip，业务暂时用不到可以不存',
  `inner_ip_from` varchar(255) DEFAULT NULL COMMENT '前端节点 内网ip ',
  `region_from` int(11) DEFAULT NULL COMMENT '前端节点 1.香港节点\n2.广州节点\n3.国内其他节点\n4.海外其他节点',
  `server_id_to` int(11) DEFAULT NULL,
  `public_ip_to` varchar(255) DEFAULT NULL COMMENT '转发节点 公网ip，业务暂时用不到可以不存',
  `inner_ip_to` varchar(255) DEFAULT NULL COMMENT '转发节点 内网ip',
  `region_to` int(11) DEFAULT NULL COMMENT '转发节点 1.香港节点\n2.广州节点\n3.国内其他节点\n4.海外其他节点',
  `show_name` varchar(255) DEFAULT NULL COMMENT '名称',
  `status` int(11) DEFAULT '1' COMMENT '0.异常 1.正常 2.正在创建 3.正在初始化 4.待关闭 5.已退',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `line_id` int(11) DEFAULT NULL COMMENT '网络路线id,  router_server_ecs_config 表的id',
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4
```

