package dao

import (
	"fastbird-dns/infra/base"
	"fastbird-dns/infra/logger"
	"fastbird-dns/internal/model"
	"strings"
)

type DnsDao struct {
	base *base.BaseDao
}

// NewDnsDao 创建一个新的DnsDao实例
func NewDnsDao() *DnsDao {
	logger.Info("Initializing DNS dao")
	return &DnsDao{base: base.NewBaseDao()}
}

// getNodeConfig 获取节点配置
func (d *DnsDao) GetNodeConfig(innerIP string) []*model.VpnAutoDnsNodeConf {
	var outs []*model.VpnAutoDnsNodeConf
	sqlStr := `select * from vpn_auto_dns_node_conf where inner_ip = ?`
	err := d.base.SelectAll(&outs, sqlStr, innerIP)
	if err != nil {
		return nil
	}
	return outs
}

// GetWhiteDomainCount 获取白名单域名数量
func (d *DnsDao) GetWhiteDomainCount(regionIdStr string) int {
	var total int
	sqlStr := `
	SELECT 
count(
DISTINCT
gdb.game_id,
gdb.game_region_id,
gdb.domain_name) as total
FROM game_domain_blackwhitelist_v2 as gdb 
LEFT JOIN game as g ON gdb.game_id = g.id
WHERE
gdb.game_region_id in (` + regionIdStr + `) 
AND gdb.is_black = 0
AND g.cate IN (2, 3)
;
	`

	d.base.SelectCustom([]interface{}{&total}, sqlStr)
	return total
}

func (d *DnsDao) GetWhiteDomainList(regionIdStr string, page, pageSize int) []*model.GameDomainBlackwhitelist {
	var outs []*model.GameDomainBlackwhitelist
	sqlStr := `
	SELECT 
DISTINCT
gdb.game_id,
gdb.game_region_id,
gdb.domain_name
FROM game_domain_blackwhitelist_v2 as gdb 
LEFT JOIN game as g ON gdb.game_id = g.id
WHERE
gdb.game_region_id in (` + regionIdStr + `) 
AND gdb.is_black = 0
AND g.cate IN (2, 3)
LIMIT ?,?
;
	`
	d.base.SelectAll(&outs, sqlStr, page, pageSize)
	return outs
}

func (d *DnsDao) InsertDomainIpResolve(domainName string, ipv4s []string, gameRegionId int) {
	// 构建批量插入的SQL语句和参数
	valueStrings := make([]string, len(ipv4s))
	valueArgs := make([]interface{}, 0, len(ipv4s)*3)

	for i := range ipv4s {
		valueStrings[i] = "(?, ?, ?)"
		valueArgs = append(valueArgs, domainName, ipv4s[i], gameRegionId)
	}

	sqlStr := `INSERT IGNORE INTO vpn_auto_domain_ip (domain_name, ip, game_region_id) VALUES ` +
		strings.Join(valueStrings, ",")

	d.base.Exec(sqlStr, valueArgs...)
}

// GetBlackGameList 获取黑名单游戏列表
func (d *DnsDao) GetBlackGameList(regionIdStr string) []*model.Game {
	var outs []*model.Game
	sqlStr := `SELECT 
DISTINCT
g.id
FROM 
game_region_relation AS grr 
LEFT JOIN game AS g ON g.id = grr.game_id
WHERE
grr.game_region_id in (` + regionIdStr + `)
AND grr.delsign = 0
AND g.delsign = 0
AND g.cate IN (2, 3)`
	d.base.SelectAll(&outs, sqlStr)
	return outs
}

// GetWhitelistDomains 获取黑名单域名列表
func (d *DnsDao) GetBlcaktDomainList(gameId int) []*model.GameDomainBlackwhitelist {
	var outs []*model.GameDomainBlackwhitelist
	sqlStr := `
	SELECT 
	DISTINCT
	gdb.domain_name
	FROM game_domain_blackwhitelist_v2 AS gdb
	WHERE 
	gdb.game_id = ?
	AND gdb.is_black = 1 
	AND gdb.delsign = 0
	`
	d.base.SelectAll(&outs, sqlStr, gameId)
	return outs
}

// 获得通用黑名单域名，查询条件为game_region_id = -1
func (d *DnsDao) GetCommonBlackDomainList() []*model.GameDomainBlackwhitelist {
	var outs []*model.GameDomainBlackwhitelist
	sqlStr := `
	SELECT 
	DISTINCT
	gdb.game_id,
	gdb.game_region_id,
	gdb.domain_name
	FROM game_domain_blackwhitelist_v2 AS gdb
	WHERE 
	gdb.game_region_id = -1
	AND gdb.is_black = 1 
	AND gdb.delsign = 0
	`
	d.base.SelectAll(&outs, sqlStr)
	return outs
}

// 查询游戏拥有的区服
func (d *DnsDao) GetGameRegionId(gameId int) []*model.GameRegionRelation {
	var outs []*model.GameRegionRelation
	sqlStr := `
	SELECT game_id, game_region_id FROM game_region_relation 
WHERE game_id = ? AND delsign = 0;
	`
	d.base.SelectAll(&outs, sqlStr, gameId)
	return outs
}

// 插入黑名单域名解析
func (d *DnsDao) InsertBlackDomainIpResolve(domainName string, ipv4s []string, regionList []*model.GameRegionRelation) {
	// 构建批量插入的SQL语句和参数
	// 计算总的插入数量 = IP数量 * 区域数量
	totalInserts := len(ipv4s) * len(regionList)
	valueStrings := make([]string, totalInserts)
	valueArgs := make([]interface{}, 0, totalInserts*3)

	// 双重循环遍历所有IP和区域组合
	insertIdx := 0
	for _, region := range regionList {
		for _, ip := range ipv4s {
			valueStrings[insertIdx] = "(?, ?, ?)"
			valueArgs = append(valueArgs, domainName, ip, region.GameRegionID)
			insertIdx++
		}
	}

	sqlStr := `INSERT IGNORE INTO vpn_auto_domain_ip (domain_name, ip, game_region_id) VALUES ` +
		strings.Join(valueStrings, ",")

	d.base.Exec(sqlStr, valueArgs...)
}

func (d *DnsDao) Ping(ip string) {
	sqlStr := `
	UPDATE vpn_auto_dns_node_conf SET last_ping = CURRENT_TIMESTAMP WHERE inner_ip = ?;
	`
	d.base.Exec(sqlStr, ip)
}
