## 表: game_domain_blackwhitelist_record

```sql
CREATE TABLE `game_domain_blackwhitelist_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `channel` int(11) DEFAULT NULL COMMENT '1-人工添加\n2-UU加速器\n3-wireshark解析\n4-虚拟机抓取\n5-通用黑名单\n6-不黑不白\n7-vpn检测上报\n8-客户端上报\n9-v2ray抓取\n',
  `channel_status` int(11) DEFAULT NULL COMMENT '-1-不黑不白需要删除\n0-白名单\n1-黑名单',
  `is_foreign` int(11) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`domain`,`channel`,`protocol`,`is_foreign`) USING BTREE,
  KEY `domain` (`domain`)
) ENGINE=InnoDB AUTO_INCREMENT=48960 DEFAULT CHARSET=utf8mb4 COMMENT='域名渠道记录表'
```

