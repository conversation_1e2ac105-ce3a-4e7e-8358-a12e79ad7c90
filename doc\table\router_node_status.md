## 表: router_node_status

```sql
CREATE TABLE `router_node_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `node_id` bigint(20) NOT NULL COMMENT '节点ID',
  `cpu_usage` double DEFAULT NULL COMMENT 'CPU使用率',
  `cpu_cores` int(11) DEFAULT NULL COMMENT 'CPU核心数',
  `logical_processors` int(11) DEFAULT NULL COMMENT '逻辑处理器数',
  `cpu_speed_mhz` double DEFAULT NULL COMMENT 'CPU频率(MHz)',
  `mem_current` bigint(20) DEFAULT NULL COMMENT '当前内存使用量',
  `mem_total` bigint(20) DEFAULT NULL COMMENT '总内存',
  `swap_current` bigint(20) DEFAULT NULL COMMENT '当前交换分区使用量',
  `swap_total` bigint(20) DEFAULT NULL COMMENT '总交换分区',
  `disk_current` bigint(20) DEFAULT NULL COMMENT '当前磁盘使用量',
  `disk_total` bigint(20) DEFAULT NULL COMMENT '总磁盘空间',
  `xray_state` varchar(50) DEFAULT NULL COMMENT 'Xray状态',
  `xray_error_msg` varchar(500) DEFAULT NULL COMMENT 'Xray错误信息',
  `xray_version` varchar(50) DEFAULT NULL COMMENT 'Xray版本',
  `uptime` bigint(20) DEFAULT NULL COMMENT '系统运行时间',
  `load_avg1` double DEFAULT NULL COMMENT '1分钟负载',
  `load_avg5` double DEFAULT NULL COMMENT '5分钟负载',
  `load_avg15` double DEFAULT NULL COMMENT '15分钟负载',
  `tcp_count` int(11) DEFAULT NULL COMMENT 'TCP连接数',
  `udp_count` int(11) DEFAULT NULL COMMENT 'UDP连接数',
  `net_up_speed` bigint(20) DEFAULT NULL COMMENT '上行速度',
  `net_down_speed` bigint(20) DEFAULT NULL COMMENT '下行速度',
  `net_traffic_sent` bigint(20) DEFAULT NULL COMMENT '发送流量',
  `net_traffic_recv` bigint(20) DEFAULT NULL COMMENT '接收流量',
  `ipv4` varchar(50) DEFAULT NULL COMMENT 'IPv4地址',
  `ipv6` varchar(50) DEFAULT NULL COMMENT 'IPv6地址',
  `app_threads` int(11) DEFAULT NULL COMMENT '应用线程数',
  `app_mem` bigint(20) DEFAULT NULL COMMENT '应用内存使用',
  `app_uptime` bigint(20) DEFAULT NULL COMMENT '应用运行时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_node_id` (`node_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节点状态表'
```

