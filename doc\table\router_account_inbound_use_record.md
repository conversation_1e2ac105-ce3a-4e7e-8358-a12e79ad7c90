## 表: router_account_inbound_use_record

```sql
CREATE TABLE `router_account_inbound_use_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `inbound_id` bigint(20) DEFAULT NULL,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `up_traffic` decimal(14,6) DEFAULT NULL COMMENT '上传流量GB',
  `down_traffic` decimal(14,6) DEFAULT NULL COMMENT '下载流量GB',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4
```

