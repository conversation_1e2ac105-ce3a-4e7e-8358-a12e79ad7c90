## 表: transaction_product

```sql
CREATE TABLE `transaction_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `platform` varchar(45) NOT NULL COMMENT '平台',
  `product_id` varchar(100) NOT NULL COMMENT '产品',
  `membership_id` int(11) DEFAULT NULL,
  `product_name` varchar(45) DEFAULT NULL,
  `currency` varchar(10) DEFAULT NULL COMMENT '货币类型',
  `price` int(11) NOT NULL COMMENT '价格',
  `first_price` int(11) DEFAULT NULL,
  `first_pay_type` int(11) NOT NULL DEFAULT '0' COMMENT '0 正常 1 首次',
  `income` float(11,2) NOT NULL COMMENT '收入',
  `income_dm` decimal(11,2) NOT NULL COMMENT '收入',
  `duration_days` int(11) NOT NULL COMMENT '增加天数',
  `discount_duration_days` int(11) NOT NULL COMMENT '额外优惠天数',
  `order_type` int(11) NOT NULL COMMENT '0:直接购买，1:订阅',
  `subscription_type` int(11) DEFAULT NULL COMMENT '1: 月, 2: 季度, 3: 年',
  `remark` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `name_UNIQUE` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=60009 DEFAULT CHARSET=utf8mb4 COMMENT='平台订阅充值产品表	'
```

