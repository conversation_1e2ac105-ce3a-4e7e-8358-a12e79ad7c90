## 表: router_server_liner_config_bak

```sql
CREATE TABLE `router_server_liner_config_bak` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `show_name` varchar(255) DEFAULT NULL COMMENT '线路名称',
  `ecs_type` int(11) NOT NULL COMMENT '运营商 1:华为 2:阿里',
  `ping_delay_ms` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT 'ping值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2000126002 DEFAULT CHARSET=utf8mb4 COMMENT='ECS线路配置表'
```

