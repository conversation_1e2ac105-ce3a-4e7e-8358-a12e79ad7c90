## 表: game_region_relation

```sql
CREATE TABLE `game_region_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) unsigned NOT NULL,
  `game_region_id` bigint(20) unsigned NOT NULL,
  `ios_delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0. 存在 1.删除',
  `delsign` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_id`,`game_region_id`) USING BTREE,
  KEY `idx_tag_id` (`game_region_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3869 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签关联表'
```

