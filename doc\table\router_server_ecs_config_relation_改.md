## 表: router_server_ecs_config_relation_改

```sql
CREATE TABLE `router_server_ecs_config_relation_改` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `show_name` varchar(255) DEFAULT NULL COMMENT '网络线路名称',
  `ecs_config_id_from` int(11) DEFAULT NULL COMMENT '国内',
  `ecs_config_id_to` int(11) DEFAULT NULL COMMENT '国外',
  `ecs_type` int(11) DEFAULT '1' COMMENT '1.华为 2.阿里云',
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4
```

