## 表: feedback_record

```sql
CREATE TABLE `feedback_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `feedback_id` bigint(20) unsigned NOT NULL COMMENT '反馈ID',
  `admin_user_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `status_before` varchar(255) DEFAULT NULL COMMENT '操作前的值',
  `status_after` varchar(255) DEFAULT NULL COMMENT '操作后的值',
  `remark` text COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_feedback_id` (`feedback_id`) USING BTREE,
  KEY `idx_admin_id` (`admin_user_id`) USING BTREE,
  KEY `idx_created_at` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='反馈处理记录表'
```

