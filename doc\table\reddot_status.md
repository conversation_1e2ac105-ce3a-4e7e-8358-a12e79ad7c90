## 表: reddot_status

```sql
CREATE TABLE `reddot_status` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `channel_biz_id` bigint(20) unsigned NOT NULL COMMENT '功能模块ID',
  `channel_data_id` bigint(20) unsigned NOT NULL COMMENT '关联的数据ID',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读:0否,1是',
  `read_time` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_user_channel_data` (`account_id`,`channel_biz_id`,`channel_data_id`) USING BTREE,
  KEY `idx_channel_map_id` (`channel_biz_id`) USING BTREE,
  KEY `idx_data_id` (`channel_data_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=745 DEFAULT CHARSET=utf8mb4 COMMENT='红点状态表'
```

