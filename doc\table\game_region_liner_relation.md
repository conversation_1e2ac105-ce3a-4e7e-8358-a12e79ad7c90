## 表: game_region_liner_relation

```sql
CREATE TABLE `game_region_liner_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_region_id` bigint(20) unsigned NOT NULL COMMENT '游戏区服id',
  `liner_id` int(10) unsigned NOT NULL COMMENT 'liner表id',
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_region_id`,`liner_id`) USING BTREE,
  KEY `idx_tag_id` (`game_region_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=931 DEFAULT CHARSET=utf8mb4 COMMENT='游戏区服-线路关联表'
```

