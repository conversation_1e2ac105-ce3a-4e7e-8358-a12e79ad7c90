## 表: nongame_domain_blackwhitelist

```sql
CREATE TABLE `nongame_domain_blackwhitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '游戏ID',
  `game_region_id` int(11) DEFAULT NULL,
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否在黑名单',
  `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `is_foreign` tinyint(4) DEFAULT NULL COMMENT '0-国内 1-国外',
  `protocol` varchar(32) DEFAULT NULL COMMENT '协议',
  `common_blacklist_id` int(11) DEFAULT NULL COMMENT '非空表示通用黑名单',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`game_id`,`game_region_id`,`domain_name`,`protocol`) USING BTREE,
  KEY `domain_name` (`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10876 DEFAULT CHARSET=utf8mb4 COMMENT='域名黑名单表'
```

