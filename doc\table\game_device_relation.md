## 表: game_device_relation

```sql
CREATE TABLE `game_device_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) unsigned NOT NULL COMMENT '用户id',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备id',
  `game_id` bigint(20) unsigned NOT NULL COMMENT '游戏id',
  `region_id` int(11) DEFAULT NULL COMMENT '游戏服务器区域id',
  `is_connected` tinyint(4) DEFAULT '0' COMMENT '是否连接 0.未连接 1.已连接',
  `create_time` timestamp NULL DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `last_connect_time` timestamp NULL DEFAULT NULL COMMENT '上次连接时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`device_id`,`account_id`,`game_id`) USING BTREE,
  KEY `idx_tag_id` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13161 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签关联表'
```

