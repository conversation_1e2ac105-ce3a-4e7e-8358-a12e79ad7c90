package tool

import (
	"fastbird-dns/infra/logger"
	"io"
	"net"
	"net/http"
	"strings"
	"time"
)

// GetLocalIP 获取宿主机的内网IP
func GetLocalIP() (string, error) {
	// 获取所有网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		logger.Error("Failed to get network interfaces:", err)
		return "", err
	}

	// 遍历所有网络接口
	for _, iface := range interfaces {
		// 跳过 loopback 和 down 的接口
		if iface.Flags&net.FlagLoopback != 0 || iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 获取接口的地址
		addrs, err := iface.Addrs()
		if err != nil {
			logger.Error("Failed to get addresses for interface", iface.Name, ":", err)
			continue
		}

		// 遍历所有地址
		for _, addr := range addrs {
			// 检查地址类型是否为 IP 网络
			if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
				// 只返回 IPv4 地址
				if ipnet.IP.To4() != nil {
					return ipnet.IP.String(), nil
				}
			}
		}
	}

	return "", nil
}

// GetPublicIP 获取宿主机的外网IP
func GetPublicIP() (string, error) {
	// 使用多个服务来获取外网IP，以提高可靠性
	ipServices := []string{
		"https://api.ipify.org",
		"https://ifconfig.me/ip",
		"https://icanhazip.com",
	}

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	var lastErr error
	for _, service := range ipServices {
		resp, err := client.Get(service)
		if err != nil {
			logger.Warn("Failed to get public IP from", service, ":", err)
			lastErr = err
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			logger.Warn("Failed to get public IP from", service, ": status code", resp.StatusCode)
			continue
		}

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			logger.Warn("Failed to read response body from", service, ":", err)
			lastErr = err
			continue
		}

		// 清理响应内容
		ip := strings.TrimSpace(string(body))
		
		// 验证IP格式
		if net.ParseIP(ip) != nil {
			return ip, nil
		}
		
		logger.Warn("Invalid IP format received from", service, ":", ip)
	}

	if lastErr != nil {
		return "", lastErr
	}
	return "", nil
}

// GetIPInfo 获取IP信息（同时返回内网IP和外网IP）
func GetIPInfo() (localIP, publicIP string) {
	localIP, err := GetLocalIP()
	if err != nil {
		logger.Error("Failed to get local IP:", err)
	}

	publicIP, err = GetPublicIP()
	if err != nil {
		logger.Error("Failed to get public IP:", err)
	}

	return localIP, publicIP
} 