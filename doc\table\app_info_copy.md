## 表: app_info_copy

```sql
CREATE TABLE `app_info_copy` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `platform` varchar(20) NOT NULL COMMENT 'ios, android, huawei',
  `app_version` varchar(20) NOT NULL COMMENT '客户端版本号',
  `build_version` varchar(20) NOT NULL COMMENT '客户端build版本号',
  `version` varchar(20) NOT NULL COMMENT '迭代版本号',
  `pc_game_version` double(20,4) DEFAULT NULL COMMENT 'PC游戏库版本',
  `download_url` text COMMENT 'android apk下载地址,pc游戏库下载地址',
  `data_download_url` text COMMENT '数据下载链接',
  `status` tinyint(4) DEFAULT NULL COMMENT '0:正常，1:审核',
  `web_url` json DEFAULT NULL COMMENT '静态网页(隐私政策、服务协议、自动续费协议、注销账号协议)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0. 存在 1.删除',
  `sub_platform` varchar(20) DEFAULT NULL COMMENT '安卓具体渠道',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4
```

