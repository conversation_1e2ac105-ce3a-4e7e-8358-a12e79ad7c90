## 表: vpn_auto_detect_ip_blacklist_record

```sql
CREATE TABLE `vpn_auto_detect_ip_blacklist_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `remoteip` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `bytes_received` bigint(20) DEFAULT '0' COMMENT '流量',
  `protocol` varchar(20) NOT NULL DEFAULT 'tcp' COMMENT '协议：tcp,udp',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip_name` (`remoteip`,`username`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT=' vpn自动扫描到的黑名单记录表'
```

