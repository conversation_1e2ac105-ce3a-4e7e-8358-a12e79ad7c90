package base

import (
	"database/sql"
	"errors"
	"fastbird-dns/infra/logger"
	"fastbird-dns/infra/mysql"
	"reflect"

	"gorm.io/gorm"
)

type BaseDao struct {
	db *gorm.DB
}

func NewBaseDao() *BaseDao {
	return &BaseDao{db: mysql.GetDB()}
}


func (p *BaseDao) Exec(sqlStr string, values ...interface{}) {
	p.db.Exec(sqlStr, values...)
}


//查询自定义返回对象
func (p *BaseDao) SelectCustom(out []interface{}, sqlStr string, values ...interface{}) error {
	type1 := reflect.TypeOf(out)
	if type1.Kind() != reflect.Slice {
		logger.Error("第一个参数必须是interface切片,sql:", sqlStr)
		return errors.New("第一个参数必须是interface切片")
	}
	if len(out) == 0 {
		logger.Error("第一个参数长度不能为空,sql:", sqlStr)
		return errors.New("第一个参数长度不能为空")
	}
	var row *gorm.DB
	if len(values) > 0 {
		row = p.db.Raw(sqlStr, values...)
	} else {
		row = p.db.Raw(sqlStr)
	}

	if err := row.Row().Scan(out...); err != nil {
		if err == sql.ErrNoRows {
			return nil
		}
		logger.Error("SelectCustom sql error:", err, " | sqlStr: ", sqlStr)
		return err
	}
	return nil
}

//查询struct对象
func (p *BaseDao) SelectOne(out interface{}, sqlStr string, values ...interface{}) error {
	type1 := reflect.TypeOf(out)
	if type1.Kind() != reflect.Ptr {
		logger.Error("第一个参数必须是指针,sql:", sqlStr)
		return errors.New("第一个参数必须是指针")
	}
	var row *gorm.DB
	if len(values) > 0 {
		row = p.db.Raw(sqlStr, values...)
	} else {
		row = p.db.Raw(sqlStr)
	}
	row.Scan(out)
	return nil
}

//查询struct列表
func (p *BaseDao) SelectAll(outs interface{}, sqlStr string, values ...interface{}) error {
	type1 := reflect.TypeOf(outs)
	if type1.Kind() != reflect.Ptr {
		logger.Error("第一个参数必须是指针,sql:", sqlStr)
		return errors.New("第一个参数必须是指针")
	}
	type2 := type1.Elem() // 解指针后的类型
	if type2.Kind() != reflect.Slice {
		logger.Error("第一个参数必须指向切片,sql:", sqlStr)
		return errors.New("第一个参数必须指向切片")
	}
	type3 := type2.Elem()
	if type3.Kind() != reflect.Ptr {
		logger.Error("切片元素必须是指针类型,sql:", sqlStr)
		return errors.New("切片元素必须是指针类型")
	}

	rows, err := p.db.Raw(sqlStr, values...).Rows()
	defer rows.Close()
	if err != nil {
		logger.Error("SelectAll sql err:", err, " | sqlStr: ", sqlStr)
		return err
	}

	for rows.Next() {
		//  type3.Elem()是User, elem是*User
		elem := reflect.New(type3.Elem()) //type1解指针 相当于User,此时新建了User
		// 传入*User
		err := p.db.ScanRows(rows, elem.Interface())
		if err != nil {
			logger.Error("SelectAll gorm err:", err, " | sqlStr: ", sqlStr)
			continue
		}
		// reflect.ValueOf(result).Elem()是[]*User，Elem是*User，newSlice是[]*User
		newSlice := reflect.Append(reflect.ValueOf(outs).Elem(), elem)
		// 扩容后的slice赋值给*outs
		reflect.ValueOf(outs).Elem().Set(newSlice)
	}
	return nil
}

//插入orm对象
func (p *BaseDao) Insert(record interface{}) error {
	type1 := reflect.TypeOf(record)
	if type1.Kind() != reflect.Ptr {
		logger.Error("record必须是指针,record:", record)
		return errors.New("record必须是指针")
	}
	result := p.db.Create(record)
	if result.Error != nil {
		logger.Error("insert record:", record, "error:", result.Error)
		return result.Error
	}
	return nil
}