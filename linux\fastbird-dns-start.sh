#!/bin/bash

# 参数
BUILD_DIR= "/opt"
BINARY_NAME="fastbird-dns"

# 启动命令
echo "Starting service..."
# 检查是否已有同名进程在运行
OLD_PID=$(pgrep ${BINARY_NAME})
if [ ! -z "$OLD_PID" ]; then
    echo "Found existing process with PID: $OLD_PID, stopping it..."
    kill $OLD_PID
    # 等待进程停止
    sleep 2
    if ps -p $OLD_PID > /dev/null; then
        echo "Process still running after kill attempt"
        exit 1
    fi
    echo "Old process stopped successfully"
fi

# 检查并创建日志目录
LOG_DIR="/opt/log/fastbird-dns"
if [ ! -d "$LOG_DIR" ]; then
    echo "Creating log directory: $LOG_DIR"
    mkdir -p "$LOG_DIR"
    if [ $? -ne 0 ]; then
        echo "Failed to create log directory"
        exit 1
    fi
fi

chmod 777 /opt/fastbird-dns
chmod 777 /opt/log/fastbird-dns
nohup /opt/fastbird-dns -log-path=/opt/log/fastbird-dns > /dev/null 2>&1 &

# 检查进程是否启动
PID=$!
if ps -p $PID > /dev/null; then
    echo "Service started successfully with PID: $PID"
else
    echo "Failed to start service"
    exit 1
fi





