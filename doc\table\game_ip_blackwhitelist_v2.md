## 表: game_ip_blackwhitelist_v2

```sql
CREATE TABLE `game_ip_blackwhitelist_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `ip_mask` varchar(255) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `is_black` int(11) DEFAULT NULL,
  `is_foreigin` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  `channel` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`ip_mask`,`protocol`,`is_foreigin`),
  KEY `ip_mask` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=3765675 DEFAULT CHARSET=utf8mb4 COMMENT='Ip最终状态表'
```

