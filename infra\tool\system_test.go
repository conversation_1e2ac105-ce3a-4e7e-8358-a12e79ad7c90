package tool

import (
	"os"
	"runtime"
	"testing"
)

func TestGetSystemInfo(t *testing.T) {
	info := GetSystemInfo()
	
	// 检查返回的map是否包含所有预期的键
	expectedKeys := []string{
		"hostname", "os", "arch", "go_version", "cpu_num", 
		"goroutines", "memory_alloc", "memory_sys", "uptime",
	}
	
	for _, key := range expectedKeys {
		if _, exists := info[key]; !exists {
			t.<PERSON><PERSON>("GetSystemInfo() missing key: %s", key)
		}
	}
	
	// 验证一些值是否合理
	if info["os"] != runtime.GOOS {
		t.Errorf("GetSystemInfo() os = %v; want %v", info["os"], runtime.GOOS)
	}
	
	if info["arch"] != runtime.GOARCH {
		t.Errorf("GetSystemInfo() arch = %v; want %v", info["arch"], runtime.GOARCH)
	}
	
	if info["go_version"] != runtime.Version() {
		t.Errorf("GetSystemInfo() go_version = %v; want %v", info["go_version"], runtime.Version())
	}
	
	if info["cpu_num"].(int) <= 0 {
		t.Errorf("GetSystemInfo() cpu_num = %v; want > 0", info["cpu_num"])
	}
	
	// 检查内存值是否为正数
	if info["memory_alloc"].(uint64) < 0 {
		t.Errorf("GetSystemInfo() memory_alloc = %v; want >= 0", info["memory_alloc"])
	}
	
	if info["memory_sys"].(uint64) < 0 {
		t.Errorf("GetSystemInfo() memory_sys = %v; want >= 0", info["memory_sys"])
	}
	
	t.Logf("System Info: %v", info)
}

func TestGetProcessInfo(t *testing.T) {
	info := GetProcessInfo()
	
	// 检查返回的map是否包含所有预期的键
	expectedKeys := []string{
		"pid", "parent_pid", "working_dir", "executable_path", 
		"command_line", "num_goroutines", "go_max_procs",
	}
	
	for _, key := range expectedKeys {
		if _, exists := info[key]; !exists {
			t.Errorf("GetProcessInfo() missing key: %s", key)
		}
	}
	
	// 验证一些值是否合理
	if info["pid"].(int) != os.Getpid() {
		t.Errorf("GetProcessInfo() pid = %v; want %v", info["pid"], os.Getpid())
	}
	
	if info["parent_pid"].(int) != os.Getppid() {
		t.Errorf("GetProcessInfo() parent_pid = %v; want %v", info["parent_pid"], os.Getppid())
	}
	
	// 检查工作目录
	wd, err := os.Getwd()
	if err == nil && info["working_dir"] != wd {
		t.Errorf("GetProcessInfo() working_dir = %v; want %v", info["working_dir"], wd)
	}
	
	// 检查可执行文件路径
	if info["executable_path"] != os.Args[0] {
		t.Errorf("GetProcessInfo() executable_path = %v; want %v", info["executable_path"], os.Args[0])
	}
	
	// 检查命令行参数
	cmdLine, ok := info["command_line"].([]string)
	if !ok {
		t.Errorf("GetProcessInfo() command_line is not a string slice")
	} else if len(cmdLine) != len(os.Args) {
		t.Errorf("GetProcessInfo() command_line length = %v; want %v", len(cmdLine), len(os.Args))
	}
	
	// 检查GOMAXPROCS
	if info["go_max_procs"].(int) != runtime.GOMAXPROCS(0) {
		t.Errorf("GetProcessInfo() go_max_procs = %v; want %v", info["go_max_procs"], runtime.GOMAXPROCS(0))
	}
	
	t.Logf("Process Info: %v", info)
} 