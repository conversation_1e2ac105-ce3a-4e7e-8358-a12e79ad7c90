## 表: nongame_ip_blackwhitelist

```sql
CREATE TABLE `nongame_ip_blackwhitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL COMMENT '游戏ID',
  `game_region_id` int(11) DEFAULT NULL,
  `ip` varchar(255) NOT NULL COMMENT 'IP地址',
  `ip_mask` varchar(64) DEFAULT NULL COMMENT '带掩码的',
  `resolve_domain` int(11) DEFAULT NULL COMMENT '域名解析出来的',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '黑名单？',
  `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `is_foreign` tinyint(4) DEFAULT NULL COMMENT '0-国内 1-国外',
  `protocol` varchar(32) DEFAULT NULL COMMENT '协议',
  `common_blacklist_id` int(11) DEFAULT NULL COMMENT '非空表示通用黑名单',
  `is_auto_detect` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否自动解析的',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_idx` (`game_id`,`game_region_id`,`ip`,`protocol`) USING BTREE,
  KEY `resolve_domain` (`resolve_domain`) USING BTREE,
  KEY `ip_mask` (`ip_mask`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5533993 DEFAULT CHARSET=utf8mb4 COMMENT='海外IP白名单表'
```

