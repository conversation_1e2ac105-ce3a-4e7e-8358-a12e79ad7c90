## 表: account_subscription_payment_platform

```sql
CREATE TABLE `account_subscription_payment_platform` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `subscription_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 订阅 1 未订阅',
  `subscription_platform` int(11) NOT NULL COMMENT '支付平台 1 Apple 2 PayPal 3  AliPay 4 WeChatPay 5 GooglePay',
  `subscription_expires_time` datetime DEFAULT NULL,
  `out_contract_code` varchar(255) DEFAULT NULL COMMENT '外部订单id',
  `contract_id` varchar(255) DEFAULT NULL COMMENT '微信签订合同id',
  `product_id` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `account_idx` (`account_id`,`subscription_platform`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4
```

