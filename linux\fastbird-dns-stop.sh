#!/bin/bash

# 参数
BUILD_DIR= "/opt"
BINARY_NAME="fastbird-dns"

# 启动命令
echo "Stopping service..."
# 检查是否已有同名进程在运行
OLD_PID=$(pgrep ${BINARY_NAME})
if [ ! -z "$OLD_PID" ]; then
    echo "Found existing process with PID: $OLD_PID, stopping it..."
    kill $OLD_PID
    # 等待进程停止
    sleep 2
    if ps -p $OLD_PID > /dev/null; then
        echo "Process still running after kill attempt"
        exit 1
    fi
    echo "Old process stopped successfully"
fi





