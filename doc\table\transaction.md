## 表: transaction

```sql
CREATE TABLE `transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL COMMENT '用户id',
  `product_id` varchar(64) NOT NULL COMMENT '付费产品id',
  `parent_identifier` varchar(64) DEFAULT NULL COMMENT '续订关联的平台订单id',
  `identifier` varchar(255) NOT NULL COMMENT '平台订单id',
  `device_uuid` varchar(64) DEFAULT NULL COMMENT '用户设备uuid',
  `device_name` varchar(64) DEFAULT NULL COMMENT '用户设备名称',
  `system_version` varchar(64) DEFAULT NULL COMMENT '用户系统版本',
  `model` varchar(64) DEFAULT NULL COMMENT '用户设备号',
  `bvrs` varchar(15) DEFAULT NULL COMMENT 'app版本',
  `ip` varchar(63) DEFAULT NULL COMMENT '支付ip',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `download_id` bigint(20) DEFAULT NULL,
  `receipt_data` text COMMENT '平台回调原始信息',
  `receipt_json` json DEFAULT NULL COMMENT '平台回调json信息',
  `signed_payload` text COMMENT '苹果订阅回调原始信息',
  `signed_payload_json` json DEFAULT NULL COMMENT '苹果内购回调json信息',
  `status_code` int(11) NOT NULL DEFAULT '0' COMMENT '0 支付失败, 1 生成订单未支付， 2 支付成功， 3 取消支付',
  `sand_box` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0:线上环境 1:沙盒环境,测试环境',
  `country_code` char(2) DEFAULT NULL,
  `province` varchar(45) DEFAULT NULL,
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 正常 1: 删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_identifier` (`identifier`) USING BTREE COMMENT 'DO NOT DELETE',
  KEY `account_id` (`account_id`) USING BTREE,
  KEY `idx_transaction_date` (`create_time`) USING BTREE,
  KEY `idx_transaction_productid` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3316 DEFAULT CHARSET=utf8mb4
```

