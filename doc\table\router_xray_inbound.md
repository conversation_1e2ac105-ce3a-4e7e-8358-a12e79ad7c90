## 表: router_xray_inbound

```sql
CREATE TABLE `router_xray_inbound` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `xray_id` int(11) DEFAULT NULL COMMENT '3x xray id',
  `server_id` bigint(20) DEFAULT NULL COMMENT '机器id',
  `account_id` bigint(20) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `protocol` int(11) DEFAULT NULL COMMENT '协议\n1.vmess\n2.vless\n3.shadowsocks\n4.socks\n5.http\n6.trojan\n7.wireguard',
  `listen_ip` varchar(255) DEFAULT NULL COMMENT '监听',
  `port` int(11) DEFAULT NULL COMMENT '端口',
  `total_traffic` decimal(14,6) DEFAULT NULL COMMENT '总流量 GB',
  `expired_time` datetime DEFAULT NULL COMMENT '到期时间',
  `status` int(11) DEFAULT NULL COMMENT '状态\n0-xray已回收\n1-已创建未连接\n2-连接中\n3-未连接待回收',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountid_idx` (`account_id`) USING BTREE,
  KEY `serverid_idx` (`server_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4
```

