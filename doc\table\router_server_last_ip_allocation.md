## 表: router_server_last_ip_allocation

```sql
CREATE TABLE `router_server_last_ip_allocation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `liner_config_id` int(11) DEFAULT NULL COMMENT 'ECS线路ID',
  `ip_last` int(11) NOT NULL COMMENT 'IP 地址的最后一段',
  `is_using` tinyint(4) DEFAULT '0' COMMENT '0 表示未占用，1 表示占用',
  `is_using_total` tinyint(4) DEFAULT '0' COMMENT '0 表示未占用，1 表示占用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_ip_last` (`liner_config_id`,`ip_last`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18035 DEFAULT CHARSET=utf8mb4
```

