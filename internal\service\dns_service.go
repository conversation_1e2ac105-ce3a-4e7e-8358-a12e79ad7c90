package service

import (
	"fastbird-dns/infra/logger"
	"fastbird-dns/infra/tool"
	"fastbird-dns/internal/dao"
	"fastbird-dns/internal/global"
	"fastbird-dns/internal/model"
	"fmt"
	"net"
	"runtime/debug"
	"sync"
	"time"
	// "time"
)

type DnsService struct {
	dnsDao    *dao.DnsDaoRemote
	taskMutex sync.Mutex
	isRunning bool
	innerIp   string
}

func NewDnsService() *DnsService {
	logger.Info("Initializing DNS service")
	return &DnsService{
		dnsDao:    dao.NewDnsDaoRemote(),
		isRunning: false,
		innerIp:   "",
	}
}

func (s *DnsService) Ping() {
	if s.innerIp == "" {
		return
	}
	logger.Info("***** Pinging MySQL I am alive! *****")
	s.dnsDao.Ping(s.innerIp)
}

func (s *DnsService) RunScheduledTask() {
	// 尝试获取锁，如果获取不到说明任务正在运行
	if !s.taskMutex.TryLock() {
		logger.Info("Task is already running, skipping this execution")
		return
	}
	defer s.taskMutex.Unlock()

	// 添加 panic 恢复
	defer func() {
		if r := recover(); r != nil {
			logger.Error("Recovered from panic in scheduled task:", r)
			logger.Error("Stack trace:", string(debug.Stack()))
		}
	}()
	logger.Info("Running scheduled task...")

	// 获取某个IP的白名单域名
	inner_ip, err := tool.GetLocalIP()
	if err != nil {
		logger.Error("Failed to get local ip:", err)
	}
	if global.GlobalAppInfo.Env == "dev" {
		inner_ip = "**********"
		logger.Info("Running in dev environment, using fixed inner_ip: ", inner_ip)
	}
	logger.Info("Local IP: ", inner_ip)
	s.innerIp = inner_ip

	s.GetWhitelistDomains(inner_ip, 50)

	s.GetBlacklistDomains(inner_ip, 50)

	s.GetCommonBlackDomainList(inner_ip)

	logger.Info("Running scheduled task success!")
}

// GetWhitelistDomains 获取白名单域名
func (s *DnsService) GetWhitelistDomains(innerIP string, pageSize int) {
	logger.Info("***** Getting whitelist domains for IP: ", innerIP, " pageSize:", pageSize)

	regionIdStr, err := s.getRegionIdStrs(innerIP)
	if err != nil {
		logger.Error("error:", err)
		return
	}
	count := s.dnsDao.GetWhiteDomainCount(regionIdStr)
	logger.Info("Whitelist domains count:", count)

	// 计算总页数
	totalPages := (count + pageSize - 1) / pageSize
	logger.Info("Whitelist Total pages:", totalPages)

	// 遍历每一页
	for page := 0; page < totalPages; page++ {
		// 获取当前页的白名单域名列表
		domainList := s.dnsDao.GetWhiteDomainList(regionIdStr, page*pageSize, pageSize)
		if len(domainList) == 0 {
			logger.Info("No domains found on page:", page)
			continue
		}
		logger.Info("Page:", page, " got ", len(domainList), " domains")
		s.resolveWhiteDomain(page, pageSize, domainList)
		time.Sleep(1000 * time.Millisecond)
	}

	logger.Info("***** Whitelist domains resolve success! *****")
}

func (s *DnsService) resolveWhiteDomain(page int, pageSize int, domainList []*model.GameDomainBlackwhitelist) {
	for index, domain := range domainList {
		domainIndex := index + page*pageSize
		ipv4s, err := s.ResolveDomain(domainIndex, domain.DomainName)
		if err != nil {
			continue
		}
		s.dnsDao.InsertDomainIpResolve(domain.DomainName, ipv4s, domain.GameRegionID)
		
	}
}

// GetBlacklistDomains 获取黑名单域名
func (s *DnsService) GetBlacklistDomains(innerIP string, pageSize int) {
	logger.Info("***** Getting blacklist domains for IP:", innerIP, " pageSize:", pageSize)

	regionIdStr, err := s.getRegionIdStrs(innerIP)
	if err != nil {
		logger.Error("error:", err)
		return
	}

	gameList := s.dnsDao.GetBlackGameList(regionIdStr)
	logger.Info("Blacklist game  count: ", len(gameList))

	// 遍历游戏列表
	for index, game := range gameList {
		// 获取游戏对应的黑名单域名列表
		domainList := s.dnsDao.GetBlcaktDomainList(int(game.ID))
		if len(domainList) == 0 {
			logger.Info("[", index, "] No blacklist domains found for game: ", game.ID)
			continue
		}
		logger.Info("[", index, "] Game:", game.ID, " got ", len(domainList), " blacklist domains")
		// 查询游戏对应的区域
		regionList := s.dnsDao.GetGameRegionId(int(game.ID))
		if len(regionList) == 0 {
			logger.Info("[", index, "] No region found for game: ", game.ID)
			continue
		}
		// 解析域名
		for _, domain := range domainList {
			ipv4s, err := s.ResolveDomain(index, domain.DomainName)
			if err != nil {
				continue
			}
			s.dnsDao.InsertBlackDomainIpResolve(domain.DomainName, ipv4s, regionList)
			time.Sleep(500 * time.Millisecond)
		}
	}

	logger.Info("***** Blacklist domains resolve success! *****")
}

// GetCommonBlackDomainList 获取通用黑名单域名
func (s *DnsService) GetCommonBlackDomainList(innerIP string) {
	logger.Info("***** Getting common blacklist domains for IP: ", innerIP)
	domainList := s.dnsDao.GetCommonBlackDomainList()
	if len(domainList) == 0 {
		logger.Warn("common blcklist is empty!")
		return
	}

	for index, domain := range domainList {
		ipv4s, err := s.ResolveDomain(index, domain.DomainName)
		if err != nil {
			continue
		}
		s.dnsDao.InsertDomainIpResolve(domain.DomainName, ipv4s, domain.GameRegionID)
		time.Sleep(500 * time.Millisecond)
	}
	logger.Info("***** Common Blacklist domains resolve success! *****")
}

// getRegionIdStrs 获取节点配置的GameRegionID拼接为逗号分割的字符串
func (s *DnsService) getRegionIdStrs(innerIP string) (string, error) {
	// 1 获取节点欧配置
	var nodeList []*model.VpnAutoDnsNodeConf = s.dnsDao.GetNodeConfig(innerIP)
	logger.Info("Node config list length:", len(nodeList))
	if len(nodeList) == 0 {
		return "", fmt.Errorf("no node config found for innerIP: %s", innerIP)
	}
	for i, node := range nodeList {
		logger.Info("Node[", i, "]: InnerIP=", node.InnerIP, ", GameRegionID=", node.GameRegionID, ", Remark=", node.Remark)
	}
	// 2 从节点获得的GameRegionID拼接为逗号分割的字符串
	regionIdStr := ""
	for i, node := range nodeList {
		if i > 0 {
			regionIdStr += ","
		}
		regionIdStr += fmt.Sprintf("%d", node.GameRegionID)
	}

	logger.Info("Game Region IDs:", regionIdStr)
	return regionIdStr, nil
}

// ResolveDomain 解析域名获取IPv4地址
func (s *DnsService) ResolveDomain(domainIndex int, domain string) ([]string, error) {
	logger.Info("Index:", domainIndex, " Resolving domain:", domain)

	// 使用net包进行DNS解析
	ips, err := net.LookupIP(domain)
	if err != nil {
		logger.Error("Failed to resolve domain:", domain, "error:", err)
		return nil, fmt.Errorf("failed to resolve domain %s: %w", domain, err)
	}

	// 存储所有IPv4地址
	var ipv4s []string

	// 遍历所有返回的IP地址,查找IPv4地址
	for _, ip := range ips {
		// 只收集IPv4地址
		if ipv4 := ip.To4(); ipv4 != nil {
			ipv4s = append(ipv4s, ipv4.String())
		}
	}

	if len(ipv4s) == 0 {
		logger.Error("No IPv4 address found for domain:", domain)
		return nil, fmt.Errorf("no IPv4 address found for domain %s", domain)
	}

	logger.Info("Successfully resolved domain:", domain, "to IPs:", ipv4s)
	return ipv4s, nil
}
