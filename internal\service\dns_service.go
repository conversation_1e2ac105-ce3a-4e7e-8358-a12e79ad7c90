package service

import (
	"fastbird-dns/infra/logger"
	"fastbird-dns/infra/tool"
	"fastbird-dns/internal/dao"
	"fastbird-dns/internal/global"
	"fastbird-dns/internal/model"
	"fmt"
	"net"
	"runtime/debug"
	"sync"
	"time"
	// "time"
)

type DnsService struct {
	dnsDao    *dao.DnsDaoRemote
	taskMutex sync.Mutex
	isRunning bool
	innerIp   string
}

func NewDnsService() *DnsService {
	logger.Info("Initializing DNS service")
	return &DnsService{
		dnsDao:    dao.NewDnsDaoRemote(),
		isRunning: false,
		innerIp:   "",
	}
}
const batchSize = 20

func (s *DnsService) Ping() {
	if s.innerIp == "" {
		return
	}
	logger.Info("***** Pinging MySQL I am alive! *****")
	s.dnsDao.Ping(s.innerIp)
}

func (s *DnsService) RunScheduledTask() {
	// 尝试获取锁，如果获取不到说明任务正在运行
	if !s.taskMutex.TryLock() {
		logger.Info("Task is already running, skipping this execution")
		return
	}
	defer s.taskMutex.Unlock()

	// 添加 panic 恢复
	defer func() {
		if r := recover(); r != nil {
			logger.Error("Recovered from panic in scheduled task:", r)
			logger.Error("Stack trace:", string(debug.Stack()))
		}
	}()
	logger.Info("Running scheduled task...")

	// 获取某个IP的白名单域名
	inner_ip, err := tool.GetLocalIP()
	if err != nil {
		logger.Error("Failed to get local ip:", err)
	}
	if global.GlobalAppInfo.Env == "dev" {
		inner_ip = "**********"
		logger.Info("Running in dev environment, using fixed inner_ip: ", inner_ip)
	}
	logger.Info("Local IP: ", inner_ip)
	s.innerIp = inner_ip

	s.GetWhitelistDomains(inner_ip, 50)

	s.GetBlacklistDomains(inner_ip, 50)

	s.GetCommonBlackDomainList(inner_ip)

	logger.Info("Running scheduled task success!")
}

// getRegionIdStrs 获取节点配置的GameRegionID拼接为逗号分割的字符串
func (s *DnsService) getRegionIdStrs(innerIP string) (string, error) {
	// 1 获取节点欧配置
	var nodeList []*model.VpnAutoDnsNodeConf = s.dnsDao.GetNodeConfig(innerIP)
	logger.Info("Node config list length:", len(nodeList))
	if len(nodeList) == 0 {
		return "", fmt.Errorf("no node config found for innerIP: %s", innerIP)
	}
	for i, node := range nodeList {
		logger.Info("Node[", i, "]: InnerIP=", node.InnerIP, ", GameRegionID=", node.GameRegionID, ", Remark=", node.Remark)
	}
	// 2 从节点获得的GameRegionID拼接为逗号分割的字符串
	regionIdStr := ""
	for i, node := range nodeList {
		if i > 0 {
			regionIdStr += ","
		}
		regionIdStr += fmt.Sprintf("%d", node.GameRegionID)
	}

	logger.Info("Game Region IDs:", regionIdStr)
	return regionIdStr, nil
}

// ResolveDomain 解析域名获取IPv4地址
func (s *DnsService) ResolveDomain(domainIndex int, domain string) ([]string, error) {
	logger.Info("Index:", domainIndex, " Resolving domain:", domain)

	// 使用net包进行DNS解析
	ips, err := net.LookupIP(domain)
	if err != nil {
		logger.Error("Failed to resolve domain:", domain, "error:", err)
		return nil, fmt.Errorf("failed to resolve domain %s: %w", domain, err)
	}

	// 存储所有IPv4地址
	var ipv4s []string

	// 遍历所有返回的IP地址,查找IPv4地址
	for _, ip := range ips {
		// 只收集IPv4地址
		if ipv4 := ip.To4(); ipv4 != nil {
			ipv4s = append(ipv4s, ipv4.String())
		}
	}

	if len(ipv4s) == 0 {
		logger.Error("No IPv4 address found for domain:", domain)
		return nil, fmt.Errorf("no IPv4 address found for domain %s", domain)
	}

	logger.Info("Successfully resolved domain:", domain, "to IPs:", ipv4s)
	return ipv4s, nil
}
