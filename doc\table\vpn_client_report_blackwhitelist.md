## 表: vpn_client_report_blackwhitelist

```sql
CREATE TABLE `vpn_client_report_blackwhitelist` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) DEFAULT NULL,
  `ip_mask` varchar(255) DEFAULT NULL,
  `is_black` tinyint(4) DEFAULT NULL,
  `protocol` varchar(32) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT '0',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip` (`ip`,`game_id`,`game_region_id`) USING BTREE,
  KEY `status` (`status`),
  KEY `ip_mask` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=199 DEFAULT CHARSET=utf8mb4 COMMENT='客户端上报黑白名单表'
```

