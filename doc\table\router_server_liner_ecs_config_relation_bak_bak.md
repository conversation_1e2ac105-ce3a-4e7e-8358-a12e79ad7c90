## 表: router_server_liner_ecs_config_relation_bak_bak

```sql
CREATE TABLE `router_server_liner_ecs_config_relation_bak_bak` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `liner_config_id` int(11) DEFAULT NULL COMMENT '线路配置表ID',
  `ecs_config_id` int(11) DEFAULT NULL COMMENT 'ECS配置表ID',
  `server_region_id` int(11) DEFAULT NULL COMMENT '服务器区域,冗余字段 ',
  `liner_type` int(11) DEFAULT NULL COMMENT '线路类型 0: 国内 1:国外 2:中间跳转隐藏层',
  `ecs_type` int(11) DEFAULT NULL COMMENT '运营商，可以放到router_server_ecs_config ',
  `is_foreign` int(11) DEFAULT NULL COMMENT '是否国外，可以放到router_server_ecs_config',
  `ip_prefix` varchar(50) NOT NULL COMMENT 'ipv4 地址前缀',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ip_prefix` (`ip_prefix`) USING BTREE,
  KEY `liner_config_id` (`liner_config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COMMENT='线路配置关联表'
```

