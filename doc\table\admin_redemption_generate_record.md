## 表: admin_redemption_generate_record

```sql
CREATE TABLE `admin_redemption_generate_record` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `admin_user_id` int(11) NOT NULL COMMENT '管理员id',
  `action` varchar(255) DEFAULT NULL COMMENT '操作',
  `redemption_group_id` int(11) NOT NULL COMMENT '兑换码组id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COMMENT='管理员生成兑换码（组）表'
```

