## 表: account_sign_out_data_record

```sql
CREATE TABLE `account_sign_out_data_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `nick_name` varchar(255) DEFAULT NULL COMMENT '昵称',
  `user_name` varchar(255) DEFAULT NULL COMMENT '用户名',
  `external_user_id` bigint(20) DEFAULT NULL COMMENT '外显id',
  `password` varchar(255) DEFAULT NULL COMMENT '密码',
  `status` int(11) DEFAULT NULL COMMENT '1-可用 其他待定',
  `phone_number` varchar(32) DEFAULT NULL COMMENT '手机号',
  `phone_area` varchar(32) DEFAULT NULL COMMENT '手机号地区',
  `channel` int(11) DEFAULT NULL COMMENT '注册渠道: 1-手机号 2-google 3-discord 4-游客 5-微信 6-苹果 7-脸书 8-推特',
  `apple_union_id` varchar(255) DEFAULT NULL COMMENT '苹果unionid',
  `apple_nickname` varchar(64) DEFAULT NULL,
  `wx_union_id` varchar(255) DEFAULT NULL,
  `wx_nickname` varchar(64) DEFAULT NULL,
  `google_email` varchar(255) DEFAULT NULL,
  `google_nickname` varchar(64) DEFAULT NULL,
  `invitation_code` char(6) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `delsign` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `nick_name` (`nick_name`),
  KEY `phone_idx` (`phone_number`,`phone_area`) USING BTREE,
  KEY `apple_idx` (`apple_union_id`) USING BTREE,
  KEY `wechat_idx` (`wx_union_id`) USING BTREE,
  KEY `google_idx` (`google_email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=540 DEFAULT CHARSET=utf8mb4 COMMENT='用户表'
```

