package model

import (
	"time"

	"gorm.io/gorm"
)

// VpnAutoDnsNodeConf 节点配置表
type VpnAutoDnsNodeConf struct {
	gorm.Model
	InnerIP      string `gorm:"column:inner_ip;type:varchar(32)"`
	GameRegionID int    `gorm:"column:game_region_id"`
	Remark       string `gorm:"column:remark;type:varchar(255);default:null"` // 描述
}

// TableName 指定表名
func (VpnAutoDnsNodeConf) TableName() string {
	return "vpn_auto_dns_node_conf"
}

// GameDomainBlackwhitelist 域名黑白名单表
type GameDomainBlackwhitelist struct {
	gorm.Model
	GameID       int64    `gorm:"column:game_id"`
	GameRegionID int    `gorm:"column:game_region_id"`
	DomainName   string `gorm:"column:domain_name;type:varchar(255)"`
	IsBlack      int    `gorm:"column:is_black"`
	IsForeign    int    `gorm:"column:is_foreign"`
	IsDel        int    `gorm:"column:is_del"`
}

func (GameDomainBlackwhitelist) TableName() string {
	return "game_domain_blackwhitelist"
}

// Game 游戏表
type Game struct {
	gorm.Model
	ID            int64  `gorm:"column:id;primaryKey;autoIncrement"`
	GameName      string `gorm:"column:game_name;type:varchar(255)"`
	Cate          int    `gorm:"column:cate"`
	GameRergionId int    `gorm:"column:game_region_id"`
}

func (Game) TableName() string {
	return "game"
}

// GameRegionRelation 游戏区服关系表
type GameRegionRelation struct {
	gorm.Model
	GameID       int64 `gorm:"column:game_id"`
	GameRegionID int `gorm:"column:game_region_id"`
}

func (GameRegionRelation) TableName() string {
	return "game_region_relation"
}

// VpnAutoDomainIpResolve 域名IP解析表
// type VpnAutoDomainIpResolve struct {
// 	gorm.Model
// 	Domain string `gorm:"column:domain;type:varchar(255)"`
// 	IP     string `gorm:"column:ip;type:varchar(32)"`
// 	Status int    `gorm:"column:status"`
// }

// func (VpnAutoDomainIpResolve) TableName() string {
// 	return "vpn_auto_domain_ip_resolve"
// }

// GameRegion 游戏区服表
type GameRegion struct {
	gorm.Model
	RegionName string `gorm:"column:region_name;type:varchar(255)"`
	RegionCode string `gorm:"column:region_code;type:varchar(50)"`
	Status     int    `gorm:"column:status"`
}

// VpnAutoDomainIp 域名解析IP结果表
type VpnAutoDomainIp struct {
	DomainName   string    `gorm:"column:domain_name;type:varchar(255);primaryKey"`
	IP           string    `gorm:"column:ip;type:varchar(255);primaryKey"`
	GameRegionID int       `gorm:"column:game_region_id;primaryKey"`
	Status       int       `gorm:"column:status;default:0"`
	CreateTime   time.Time `gorm:"column:create_time;autoCreateTime"`
	UpdateTime   time.Time `gorm:"column:update_time;autoUpdateTime"`
}

func (VpnAutoDomainIp) TableName() string {
	return "vpn_auto_domain_ip"
}


func (GameRegion) TableName() string {
	return "game_region"
}

// VpnAutoResolveLog DNS解析日志表
type VpnAutoResolveLog struct {
	gorm.Model
	Domain      string    `gorm:"column:domain;type:varchar(255)"`
	IP          string    `gorm:"column:ip;type:varchar(32)"`
	NodeIP      string    `gorm:"column:node_ip;type:varchar(32)"`
	ResolveTime time.Time `gorm:"column:resolve_time"`
	Status      int       `gorm:"column:status"`
}

func (VpnAutoResolveLog) TableName() string {
	return "vpn_auto_resolve_log"
}

// VpnAutoNodeStatus 节点状态表
type VpnAutoNodeStatus struct {
	gorm.Model
	NodeIP        string    `gorm:"column:node_ip;type:varchar(32)"`
	Status        int       `gorm:"column:status"`
	LastHeartbeat time.Time `gorm:"column:last_heartbeat"`
}

func (VpnAutoNodeStatus) TableName() string {
	return "vpn_auto_node_status"
}

// VpnAutoConfig 系统配置表
type VpnAutoConfig struct {
	gorm.Model
	ConfigKey   string `gorm:"column:config_key;type:varchar(100)"`
	ConfigValue string `gorm:"column:config_value;type:text"`
	Description string `gorm:"column:description;type:varchar(255)"`
}

func (VpnAutoConfig) TableName() string {
	return "vpn_auto_config"
}
