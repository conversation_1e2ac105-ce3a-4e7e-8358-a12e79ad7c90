## 表: extent_event_record

```sql
CREATE TABLE `extent_event_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `oaid` varchar(255) DEFAULT NULL,
  `action_type` int(11) NOT NULL COMMENT '		1：应用激活\n		2：启动应用\n		3：次日留存\n		4：付费\n		5：提交表单\n		6：授信\n		7：注册\n		9：线索收集页面访问\n		10：老客激活\n		11：完件\n		12：支用\n		13：还款\n		14：申请\n		18：下单\n		21：预约\n		101：关键行为1\n		102：关键行为2\n		1001：平台首日ROI\n',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='华为推广事件记录表'
```

