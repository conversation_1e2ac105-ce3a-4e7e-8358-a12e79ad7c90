## 表: game

```sql
CREATE TABLE `game` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '游戏名称',
  `subtitle` varchar(100) DEFAULT NULL COMMENT '子标题',
  `apple_app_id` varchar(64) DEFAULT NULL,
  `android_bundle_id` varchar(100) DEFAULT NULL COMMENT 'Android BundleId',
  `pc_exe_file` varchar(100) DEFAULT NULL COMMENT 'PC游戏执行文件名称',
  `pc_process_exe` varchar(255) DEFAULT NULL COMMENT '进程名',
  `pc_display_name` varchar(100) DEFAULT NULL COMMENT 'pc游戏显示名称',
  `pc_display_name_loc` json DEFAULT NULL COMMENT 'pc游戏显示名称, json',
  `image_url` varchar(255) NOT NULL COMMENT '游戏图片',
  `game_url` text COMMENT '游戏链接地址，官网，下载地址等',
  `startup_url` json DEFAULT NULL COMMENT '{"steam":"","exe":"","epic":"","blizzard":"","ea":"","wegame":"","ubisoft":""}',
  `is_foreign` tinyint(4) NOT NULL COMMENT '0.国内ip 1.国外ip',
  `is_free` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:不免费加速, 1:免费加速',
  `is_show` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:不外显, 1:外显',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，降序',
  `default_region` int(11) DEFAULT '-1' COMMENT '默认区服，自动区服',
  `is_default` int(11) NOT NULL DEFAULT '0' COMMENT '0. 非默认，1. 默认',
  `delay` int(11) NOT NULL DEFAULT '0' COMMENT '网络延迟',
  `mark` varchar(500) DEFAULT NULL COMMENT '游戏简介',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `game_id` bigint(20) DEFAULT '0' COMMENT 'game表唯一id，0表示未关联',
  `cate` bigint(20) NOT NULL DEFAULT '0' COMMENT '0:线路，1:影音，2:移动游戏，3:pc游戏',
  `ios_delsign` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0. 存在 1.删除',
  `delsign` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_created_at` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6738897447 DEFAULT CHARSET=utf8mb4 COMMENT='游戏信息表'
```

