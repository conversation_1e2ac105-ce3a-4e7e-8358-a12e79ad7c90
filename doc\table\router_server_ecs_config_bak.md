## 表: router_server_ecs_config_bak

```sql
CREATE TABLE `router_server_ecs_config_bak` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '创建esc的配置表',
  `region` int(11) NOT NULL COMMENT '前端节点 1.香港节点\n2.广州节点\n',
  `region_str` varchar(50) NOT NULL COMMENT '区域字符串。阿里对应regionId',
  `zone_str` varchar(50) DEFAULT NULL COMMENT '阿里 实例所属的可用区ID',
  `project_id` varchar(50) NOT NULL COMMENT '项目id',
  `security_group_id` text NOT NULL COMMENT '安全组id',
  `subnet_id` varchar(50) NOT NULL COMMENT '子网id。 阿里 虚拟交换机ID vSwitchId ',
  `ip_prefix` varchar(50) NOT NULL COMMENT 'ipv4 地址前缀',
  `image_ref` varchar(50) NOT NULL COMMENT '镜像',
  `flavor_ref` varchar(50) NOT NULL COMMENT '系统规格。阿里 实例的资源规格instanceType ',
  `name` varchar(50) NOT NULL COMMENT '名字',
  `vpc_id` varchar(50) DEFAULT NULL COMMENT '指定已创建VPC的ID',
  `version` int(11) NOT NULL DEFAULT '1' COMMENT '配置版本',
  `remark` text NOT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `charging_mode` varchar(50) NOT NULL COMMENT 'prePaid周期付款，postPaid按需付款。 阿里对应字段：instanceChargeType PostPaid:按需付费 ',
  `period_type` varchar(50) NOT NULL DEFAULT '' COMMENT 'charging_mode=prePaid起作用，只有month,year两种',
  `delsign` tinyint(4) NOT NULL DEFAULT '1',
  `internet_charge_type` varchar(255) DEFAULT NULL COMMENT '阿里云网络计费类型:PayByBandwidth-按固定带宽计费,PayByTraffic-按使用流量计费',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=261005 DEFAULT CHARSET=utf8mb4 COMMENT='esc云服务器表'
```

