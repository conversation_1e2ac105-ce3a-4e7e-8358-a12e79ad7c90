echo "parameter1:" $1


# 先关闭远程服务
sshpass -p "Mangosteen0!" ssh -o StrictHostKeyChecking=no root@$1 "bash /opt/fastbird-dns-stop.sh"

# 再删除已存在的二进制 
sshpass -p "Mangosteen0!" ssh -o StrictHostKeyChecking=no root@$1 "rm -rf /opt/fastbird-dns"

# 运行软件推送到目标机器
sshpass -p "Mangosteen0!" scp   /root/fastbird-dns/linux/*  root@$1:/opt/

echo "执行目标机器启动脚本: " $1

# 执行目标机器启动脚本
sshpass -p "Mangosteen0!" ssh -o StrictHostKeyChecking=no root@$1 "bash /opt/fastbird-dns-start.sh"