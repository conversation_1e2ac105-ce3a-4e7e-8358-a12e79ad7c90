## 表: transaction_googleplay_callback_log

```sql
CREATE TABLE `transaction_googleplay_callback_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` varchar(128) DEFAULT NULL COMMENT 'googleplay的消息id',
  `transaction_id` bigint(20) DEFAULT NULL COMMENT 'transaction表id',
  `callback_type` varchar(100) DEFAULT NULL COMMENT '回调的事件类型',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `publish_time` timestamp NULL DEFAULT NULL COMMENT 'googleplay的消息发布时间',
  `subscription` varchar(128) DEFAULT NULL COMMENT 'pub/sub队列名',
  `package_name` varchar(255) DEFAULT NULL COMMENT '包名',
  `event_time_millis` timestamp NULL DEFAULT NULL COMMENT '事件真实发生时间',
  `ot_notification_type` int(11) DEFAULT NULL COMMENT '一次性购买通知类型',
  `ot_purchase_token` text COMMENT '一次性购买token',
  `ot_sku` varchar(128) DEFAULT NULL COMMENT '一次性购买产品名',
  `is_success` int(11) DEFAULT NULL COMMENT '0-失败 1-成功',
  `failed_reason` text COMMENT '失败原因',
  `google_order_id` varchar(128) DEFAULT NULL COMMENT 'google订单id',
  `void_purchase_token` text COMMENT '取消/退款通知token',
  `void_product_type` int(11) DEFAULT NULL,
  `void_refund_type` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `message_id_idx` (`message_id`) USING BTREE,
  KEY `idx_transaction_date` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4
```

