CREATE TABLE `vpn_auto_domain_ip_resolve` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `ip` varchar(255) NOT NULL COMMENT '解析IP',
  `game_id` int(11) NOT NULL COMMENT '游戏id',
  `game_region_id` int(11) NOT NULL COMMENT '游戏区服id',
  `status` int(4) NOT NULL DEFAULT '0' COMMENT '处理状态 0-未处理 1-已处理',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `domain_name` (`domain_name`,`ip`,`is_foreign`,`game_id`,`game_region_id`) USING BTREE,
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='域名解析IP结果表';

