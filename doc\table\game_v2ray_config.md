## 表: game_v2ray_config

```sql
CREATE TABLE `game_v2ray_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` bigint(20) NOT NULL DEFAULT '0',
  `game_name` varchar(255) DEFAULT NULL,
  `game_region_name` varchar(255) DEFAULT '国服',
  `client_uuid` varchar(255) DEFAULT NULL,
  `client_username` varchar(255) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `vless_ip` varchar(255) DEFAULT NULL,
  `vless_port` int(11) DEFAULT NULL,
  `test_vless_port` int(11) DEFAULT NULL,
  `game_is_foreign` int(11) DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 未操作 1: 开始 2: 结束 3: 记录 4: 测试 5: 同步 6: 已测',
  `user_id` bigint(20) DEFAULT NULL,
  `state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:default 1:ok 2: testing 3:pass 4:error  5:测试通过 6:已同步 7:tapd 8:无路由模式 9: 无权限 10:已验证 11:验证失败',
  `vpn_region` varchar(32) DEFAULT NULL COMMENT 'V2ray地区tag',
  `game_is_pc` int(11) NOT NULL DEFAULT '0' COMMENT '是否pc。0-非 1-是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_uni` (`game_id`,`game_region_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22347 DEFAULT CHARSET=utf8mb4
```

