## 表: transaction_log

```sql
CREATE TABLE `transaction_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `platform` varchar(50) NOT NULL COMMENT '平台',
  `identifier` varchar(64) DEFAULT NULL COMMENT '平台订单id',
  `callback_type` varchar(100) DEFAULT NULL COMMENT '回调的事件类型',
  `receipt_data` text COMMENT '平台回调原始信息',
  `receipt_json` json DEFAULT NULL COMMENT '平台回调json信息',
  `signed_payload` text COMMENT '苹果订阅回调原始信息',
  `signed_payload_json` json DEFAULT NULL COMMENT '苹果内购回调json信息',
  `sand_box` tinyint(1) DEFAULT '0' COMMENT '0:线上环境 1:沙盒环境,测试环境',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_transaction_date` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2957 DEFAULT CHARSET=utf8mb4
```

