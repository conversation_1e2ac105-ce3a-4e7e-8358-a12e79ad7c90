## 表: game_bak

```sql
CREATE TABLE `game_bak` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '游戏名称',
  `apple_app_id` varchar(64) DEFAULT NULL,
  `android_bundle_id` varchar(100) DEFAULT NULL COMMENT 'Android BundleId',
  `image_url` varchar(255) NOT NULL COMMENT '游戏图片',
  `is_foreign` tinyint(4) NOT NULL COMMENT '0.国内ip 1.国外ip',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，降序',
  `is_default` int(11) NOT NULL DEFAULT '0' COMMENT '0. 非默认，1. 默认',
  `delay` int(11) NOT NULL DEFAULT '0' COMMENT '网络延迟',
  `mark` varchar(500) DEFAULT NULL COMMENT '游戏简介',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `delsign` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_created_at` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6738897193 DEFAULT CHARSET=utf8mb4 COMMENT='游戏信息表'
```

