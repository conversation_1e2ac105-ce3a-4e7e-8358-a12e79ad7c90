package service

import (
	"fastbird-dns/infra/logger"
	"fastbird-dns/internal/model"
	"fastbird-dns/internal/vo"
	"time"
)

// GetWhitelistDomains 获取白名单域名
func (s *DnsService) GetWhitelistDomains(innerIP string, pageSize int) {
	logger.Info("***** Getting whitelist domains for IP: ", innerIP, " pageSize:", pageSize)

	regionIdStr, err := s.getRegionIdStrs(innerIP)
	if err != nil {
		logger.Error("error:", err)
		return
	}
	count := s.dnsDao.GetWhiteDomainCount(regionIdStr)
	logger.Info("Whitelist domains count:", count)

	// 计算总页数
	totalPages := (count + pageSize - 1) / pageSize
	logger.Info("Whitelist Total pages:", totalPages)

	// 遍历每一页
	for page := 0; page < totalPages; page++ {
		// 获取当前页的白名单域名列表
		domainList := s.dnsDao.GetWhiteDomainList(regionIdStr, page*pageSize, pageSize)
		if len(domainList) == 0 {
			logger.Info("No domains found on page:", page)
			continue
		}
		logger.Info("Page:", page, " got ", len(domainList), " domains")
		s.resolveWhiteDomain(page, pageSize, domainList)
		time.Sleep(1000 * time.Millisecond)
	}

	logger.Info("***** Whitelist domains resolve success! *****")
}

// 解析白名单并插入db
func (s *DnsService) resolveWhiteDomain(page int, pageSize int, domainList []*model.GameDomainBlackwhitelist) {
	var requests []vo.InsertDomainIpResolveRequest

	for index, domain := range domainList {
		domainIndex := index + page*pageSize
		ipv4s, err := s.ResolveDomain(domainIndex, domain.DomainName)
		if err != nil {
			continue
		}

		requests = append(requests, vo.InsertDomainIpResolveRequest{
			DomainName:   domain.DomainName,
			Ipv4s:        ipv4s,
			GameRegionId: domain.GameRegionID,
		})

		// When batch size is reached or it's the last item, insert the batch
		if len(requests) >= batchSize || index == len(domainList)-1 {
			if len(requests) > 0 {
				s.dnsDao.InsertDomainIpResolve(requests)
				requests = requests[:0] // Clear the slice while keeping capacity
			}
		}
	}
}
