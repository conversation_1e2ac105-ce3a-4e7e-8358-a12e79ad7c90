
package vo
// 请求和响应结构体定义

// GetNodeConfigRequest 获取节点配置请求
type GetNodeConfigRequest struct {
	InnerIp string `json:"innerIp"`
}

// NodeConfigResponse 节点配置响应
type NodeConfigResponse struct {
	InnerIp      string `json:"innerIp"`
	GameRegionId int    `json:"gameRegionId"`
	Remark       string `json:"remark"`
}

// NodeConfigListResponse 节点配置列表响应
type NodeConfigListResponse struct {
	NodeConfigs []NodeConfigResponse `json:"nodeConfigs"`
}

// GetWhiteDomainCountRequest 获取白名单域名数量请求
type GetWhiteDomainCountRequest struct {
	RegionIdStr string `json:"regionIdStr"`
}

// WhiteDomainCountResponse 白名单域名数量响应
type WhiteDomainCountResponse struct {
	Total int `json:"total"`
}

// GetWhiteDomainListRequest 获取白名单域名列表请求
type GetWhiteDomainListRequest struct {
	RegionIdStr string `json:"regionIdStr"`
	Page        int    `json:"page"`
	PageSize    int    `json:"pageSize"`
}

// DomainBlackwhitelistResponse 域名黑白名单响应
type DomainBlackwhitelistResponse struct {
	GameId       int64  `json:"gameId"`
	GameRegionId int    `json:"gameRegionId"`
	DomainName   string `json:"domainName"`
}

// WhiteDomainListResponse 白名单域名列表响应
type WhiteDomainListResponse struct {
	DomainList []DomainBlackwhitelistResponse `json:"domainList"`
	Total      int                            `json:"total"`
}

// InsertDomainIpResolveRequest 插入域名IP解析请求
type InsertDomainIpResolveRequest struct {
	DomainName   string   `json:"domainName"`
	Ipv4s        []string `json:"ipv4s"`
	GameRegionId int      `json:"gameRegionId"`
}

// GetBlackGameListRequest 获取黑名单游戏列表请求
type GetBlackGameListRequest struct {
	RegionIdStr string `json:"regionIdStr"`
}

// GameResponse 游戏响应
type GameResponse struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Cate int64  `json:"cate"`
}

// BlackGameListResponse 黑名单游戏列表响应
type BlackGameListResponse struct {
	GameList []GameResponse `json:"gameList"`
}

// GetBlackDomainListRequest 获取黑名单域名列表请求
type GetBlackDomainListRequest struct {
	GameId int `json:"gameId"`
}

// BlackDomainListResponse 黑名单域名列表响应
type BlackDomainListResponse struct {
	DomainList []DomainBlackwhitelistResponse `json:"domainList"`
}

// CommonBlackDomainListResponse 通用黑名单域名列表响应
type CommonBlackDomainListResponse struct {
	DomainList []DomainBlackwhitelistResponse `json:"domainList"`
}

// GetGameRegionIdRequest 获取游戏区域ID请求
type GetGameRegionIdRequest struct {
	GameId int `json:"gameId"`
}

// GameRegionRelationResponse 游戏区域关系响应
type GameRegionRelationResponse struct {
	GameId       int64 `json:"gameId"`
	GameRegionId int64 `json:"gameRegionId"`
}

// GameRegionIdListResponse 游戏区域ID列表响应
type GameRegionIdListResponse struct {
	RegionList []GameRegionRelationResponse `json:"regionList"`
}

// GameRegionRelationItem 游戏区域关系项
type GameRegionRelationItem struct {
	GameId       int64 `json:"gameId"`
	GameRegionId int64 `json:"gameRegionId"`
}

// InsertBlackDomainIpResolveRequest 插入黑名单域名IP解析请求
type InsertBlackDomainIpResolveRequest struct {
	DomainName string                   `json:"domainName"`
	Ipv4s      []string                 `json:"ipv4s"`
	RegionList []GameRegionRelationItem `json:"regionList"`
}

// PingRequest Ping请求
type PingRequest struct {
	Ip string `json:"ip"`
}

// OperationResultResponse 操作结果响应
type OperationResultResponse struct {
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	AffectedRows int    `json:"affectedRows"`
}

// ApiResponse 统一API响应格式
type ApiResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}