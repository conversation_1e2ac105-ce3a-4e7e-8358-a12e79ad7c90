## 表: vpn_auto_detect_ip_blacklist

```sql
CREATE TABLE `vpn_auto_detect_ip_blacklist` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `ip` varchar(32) DEFAULT NULL,
  `protocol` varchar(20) NOT NULL DEFAULT 'tcp' COMMENT '协议：tcp,udp',
  `is_black` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1: 黑，0: 白',
  `status` int(4) NOT NULL DEFAULT '0' COMMENT '处理状态 0-未处理 1-已处理',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `system_channel` tinyint(4) DEFAULT NULL COMMENT '1 苹果 2 安卓 3 pc 4 鸿蒙',
  PRIMARY <PERSON> (`id`),
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`ip`,`protocol`,`is_black`,`system_channel`) USING BTREE,
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=550169 DEFAULT CHARSET=utf8mb4 COMMENT='ip黑名单自动获取'
```

