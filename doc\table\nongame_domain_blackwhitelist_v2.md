## 表: nongame_domain_blackwhitelist_v2

```sql
CREATE TABLE `nongame_domain_blackwhitelist_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `domain_name` varchar(255) DEFAULT NULL,
  `is_black` int(11) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `is_foreigin` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  `channel` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`domain_name`,`protocol`,`is_foreigin`),
  KEY `domain_name` (`domain_name`)
) ENGINE=InnoDB AUTO_INCREMENT=93164 DEFAULT CHARSET=utf8mb4
```

