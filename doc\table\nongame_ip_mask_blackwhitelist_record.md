## 表: nongame_ip_mask_blackwhitelist_record

```sql
CREATE TABLE `nongame_ip_mask_blackwhitelist_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ip_mask` varchar(255) NOT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `channel` int(11) DEFAULT NULL,
  `channel_status` int(11) DEFAULT NULL,
  `resolve_domain` bigint(20) DEFAULT NULL,
  `is_foreign` int(11) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ip_mask` (`game_id`,`game_region_id`,`ip_mask`,`channel`,`is_foreign`,`protocol`) USING BTREE,
  KEY `ip_mask_2` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=234037 DEFAULT CHARSET=utf8mb4
```

