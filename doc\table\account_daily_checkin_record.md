## 表: account_daily_checkin_record

```sql
CREATE TABLE `account_daily_checkin_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) unsigned NOT NULL,
  `checkin_date` date NOT NULL COMMENT '签到日期',
  `consecutive_days` int(11) NOT NULL DEFAULT '0' COMMENT '累计签到天数',
  `reward` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '赠送时间，小时',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `platform` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1mobile,2pc,3xbox',
  `bind_phone` varchar(255) DEFAULT NULL COMMENT '绑定的手机号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_account_date` (`account_id`,`checkin_date`,`platform`) USING BTREE,
  UNIQUE KEY `unique_account_con` (`account_id`,`consecutive_days`,`platform`) USING BTREE,
  KEY `bind_phone_con` (`bind_phone`,`consecutive_days`,`platform`) USING BTREE,
  KEY `bind_phone_date` (`bind_phone`,`checkin_date`,`platform`)
) ENGINE=InnoDB AUTO_INCREMENT=232 DEFAULT CHARSET=utf8mb4 COMMENT='用户每日签到记录表'
```

