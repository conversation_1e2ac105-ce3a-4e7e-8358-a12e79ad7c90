## 表: router_xray_inbound_socks_extend

```sql
CREATE TABLE `router_xray_inbound_socks_extend` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inbound_id` bigint(20) DEFAULT NULL,
  `user` varchar(255) DEFAULT NULL COMMENT 'id',
  `pass` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inboundid_idx` (`inbound_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4
```

