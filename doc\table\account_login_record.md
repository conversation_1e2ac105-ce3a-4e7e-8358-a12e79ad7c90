## 表: account_login_record

```sql
CREATE TABLE `account_login_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `token` varchar(255) NOT NULL,
  `client_ip` varchar(32) DEFAULT NULL COMMENT '登录ip',
  `oaid` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `token_uni` (`token`) USING BTREE,
  KEY `accountid_idx` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10318 DEFAULT CHARSET=utf8mb4 COMMENT='登录记录表'
```

