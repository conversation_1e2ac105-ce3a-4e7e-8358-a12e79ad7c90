## 表: game_server_region_relation

```sql
CREATE TABLE `game_server_region_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_region_id` bigint(20) unsigned NOT NULL COMMENT '游戏区服',
  `server_region_id` bigint(20) unsigned NOT NULL COMMENT '服务器地区',
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_region_id`,`server_region_id`) USING BTREE,
  KEY `idx_tag_id` (`game_region_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=608 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签关联表'
```

