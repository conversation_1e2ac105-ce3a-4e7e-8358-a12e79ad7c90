## 表: redemption_code

```sql
CREATE TABLE `redemption_code` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `membership_id` int(11) NOT NULL COMMENT '会员等级1手游会员 2黄金会员 3白金会员',
  `redemption_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1付费用户邀请码 2主播推广码  3官方限时兑换码 4官方不限时码',
  `reward` decimal(5,2) NOT NULL COMMENT '小时',
  `code` varchar(255) NOT NULL COMMENT '兑换码',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '生效时间可以为空',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间可以为空',
  `business_table_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '业务表的主键id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `redemption_group_id` int(11) NOT NULL DEFAULT '1' COMMENT '兑换码组ID',
  `code_desc` varchar(255) DEFAULT NULL COMMENT '兑换码描述信息',
  `max_num` int(11) NOT NULL DEFAULT '0' COMMENT '0表示无限',
  `used_num` int(11) NOT NULL DEFAULT '0' COMMENT '已使用数量',
  `delsign` tinyint(1) NOT NULL DEFAULT '0',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11039 DEFAULT CHARSET=utf8mb4 COMMENT='兑换码（单条）表'
```

