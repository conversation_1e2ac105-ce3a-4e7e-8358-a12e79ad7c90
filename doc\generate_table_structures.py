import pymysql
import logging

# 配置日志记录
logging.basicConfig(filename='table_structures.log', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logging.getLogger().addHandler(logging.StreamHandler())

def get_table_structures(host, port, user, password, db):
    try:
        # 连接到数据库
        connection = pymysql.connect(host=host, port=port, user=user, password=password, db=db)
        cursor = connection.cursor()

        # 获取所有表名
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()

        # 为每个表获取结构
        table_structures = {}
        for table in tables:
            table_name = table[0]
            if not table_name.startswith(("旧", "老")):
                cursor.execute(f"SHOW CREATE TABLE `{table_name}`")
                create_table = cursor.fetchone()[1]
                table_structures[table_name] = create_table

    except Exception as e:
        logging.error(f"Error: {e}")
        return None
    finally:
        # 确保关闭数据库连接
        if cursor:
            cursor.close()
        if connection:
            connection.close()

    return table_structures

def save_table_structures_to_markdown(table_structures):
    try:
        for table_name, create_table in table_structures.items():
            file_path = f'doc/table/{table_name}.md'
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(f"## 表: {table_name}\n\n")
                file.write(f"```sql\n{create_table}\n```\n\n")
            logging.info(f"表结构已保存到 {file_path}")
        # 删除以“旧”或“老”开头的表对应的Markdown文件
        for file_name in os.listdir('doc/table'):
            if file_name.endswith('.md') and file_name.startswith(("旧", "老")):
                os.remove(os.path.join('doc', file_name))
                logging.info(f"已删除 {file_name}")
    except Exception as e:
        logging.error(f"保存表结构时出错: {e}")

if __name__ == "__main__":
    host = 'coder.53site.com'
    port = 3306
    user = 'go_wf_activity_api'
    password = 'Mangosteen0!'
    db = 'vpn-cloud'
    output_file = 'doc/table_structures.md'

    table_structures = get_table_structures(host, port, user, password, db)
    if table_structures is not None:
        logging.info("开始保存表结构...")
        save_table_structures_to_markdown(table_structures)
        logging.info("表结构保存完成.")