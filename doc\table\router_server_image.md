## 表: router_server_image

```sql
CREATE TABLE `router_server_image` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `image_ref` varchar(255) DEFAULT NULL COMMENT '镜像ID',
  `name` varchar(255) DEFAULT NULL COMMENT '镜像名称',
  `version` float(16,4) DEFAULT '2.0000' COMMENT '镜像版本号',
  `server_region` int(11) DEFAULT '0' COMMENT '服务器区域id',
  `job_id` varchar(255) DEFAULT NULL,
  `status` tinyint(4) DEFAULT '0' COMMENT '1 创建完成  2 正在创建 ',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 正常, 1: 删除',
  `from_router_server_id` int(11) DEFAULT NULL COMMENT '镜像源的服务器id（阿里镜像新建模式非空）',
  `from_router_server_image_id` int(11) DEFAULT NULL COMMENT '镜像拷贝的原id(阿里镜像拷贝模式非空)',
  PRIMARY KEY (`id`),
  KEY `image_ref` (`image_ref`)
) ENGINE=InnoDB AUTO_INCREMENT=141 DEFAULT CHARSET=utf8mb4
```

