package mysql

import (
	"context"
	"errors"
	"fastbird-dns/infra/logger"
	"fmt"
	"strings"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
)

type Config struct {
	Host         string
	Port         int
	User         string
	Password     string
	Database     string
	MaxIdleConns int
	MaxOpenConns int
}

var defaultDB *gorm.DB

// 自定义GORM日志适配器
type GormLoggerAdapter struct {
	SlowThreshold             time.Duration
	IgnoreRecordNotFoundError bool
	LogLevel                  gormlogger.LogLevel
}

// 实现gorm.logger.Interface接口
func (l *GormLoggerAdapter) LogMode(level gormlogger.LogLevel) gormlogger.Interface {
	newLogger := *l
	newLogger.LogLevel = level
	return &newLogger
}

func (l *GormLoggerAdapter) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= gormlogger.Info {
		logger.Info(fmt.Sprintf(msg, data...))
	}
}

func (l *GormLoggerAdapter) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= gormlogger.Warn {
		logger.Warn(fmt.Sprintf(msg, data...))
	}
}

func (l *GormLoggerAdapter) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= gormlogger.Error {
		logger.Error(fmt.Sprintf(msg, data...))
	}
}

func (l *GormLoggerAdapter) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.LogLevel <= gormlogger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()
	
	// 格式化SQL语句，移除多余空格
	sql = strings.ReplaceAll(sql, "\n", " ")
	sql = strings.ReplaceAll(sql, "\t", " ")
	sql = strings.TrimSpace(sql)
	
	// 构建详细的SQL执行信息
	sqlInfo := fmt.Sprintf("[%.3fms] [rows:%v] %s", 
		float64(elapsed.Nanoseconds())/1e6, 
		rows, 
		sql)

	// 根据错误级别和执行时间记录日志
	switch {
	case err != nil && l.LogLevel >= gormlogger.Error && (!l.IgnoreRecordNotFoundError || !errors.Is(err, gorm.ErrRecordNotFound)):
		// 错误日志保持 ERROR 级别
		logger.Error(fmt.Sprintf("%s | %s", sqlInfo, err))
	case elapsed > l.SlowThreshold && l.SlowThreshold != 0 && l.LogLevel >= gormlogger.Warn:
		// 慢查询使用 WARN 级别
		logger.Warn(fmt.Sprintf("%s | SLOW SQL >= %v", sqlInfo, l.SlowThreshold))
	default:
		// 正常的SQL执行信息使用 DEBUG 级别
		logger.Debug(sqlInfo)
	}
}

// InitDB 初始化数据库连接
func InitDB(cfg *Config) error {
	logger.Info("Initializing MySQL connection with config:")
	logger.Info("Host:", cfg.Host)
	logger.Info("Port:", cfg.Port)
	logger.Info("User:", cfg.User)
	logger.Info("Database:", cfg.Database)
	logger.Info("MaxIdleConns:", cfg.MaxIdleConns)
	logger.Info("MaxOpenConns:", cfg.MaxOpenConns)
	logger.Info("Timeout:", 60*time.Second)

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local&timeout=60s",
		cfg.User, cfg.Password, cfg.Host, cfg.Port, cfg.Database)

	// 配置自定义GORM日志
	customLogger := &GormLoggerAdapter{
		SlowThreshold:             time.Second, // 慢SQL阈值
		IgnoreRecordNotFoundError: true,        // 忽略记录未找到错误
		LogLevel:                  gormlogger.Info,
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: customLogger,
	})
	if err != nil {
		return fmt.Errorf("connect to mysql failed: %w", err)
	}

	// 获取底层的sqlDB
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("get sql.DB failed: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Hour) // 连接最大生命周期

	// 测试连接
	if err = sqlDB.Ping(); err != nil {
		return fmt.Errorf("ping mysql failed: %w", err)
	}

	logger.Info("MySQL connection established successfully")
	defaultDB = db
	return nil
}

// GetDB 获取默认的GORM数据库连接
func GetDB() *gorm.DB {
	return defaultDB
}

// CloseDB 安全关闭数据库连接池
func CloseDB() error {
	if defaultDB == nil {
		return nil
	}

	logger.Info("Closing database connection pool...")
	
	// 获取底层的sqlDB
	sqlDB, err := defaultDB.DB()
	if err != nil {
		logger.Error("Failed to get sql.DB:", err)
		return fmt.Errorf("get sql.DB failed: %w", err)
	}

	// 创建一个带超时的上下文，确保关闭操作不会无限期等待
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 设置连接池参数，停止接受新的连接
	sqlDB.SetMaxIdleConns(0)
	sqlDB.SetMaxOpenConns(0)
	
	// 使用带超时的上下文关闭连接池
	errChan := make(chan error, 1)
	go func() {
		errChan <- sqlDB.Close()
	}()

	// 等待关闭完成或超时
	select {
	case err := <-errChan:
		if err != nil {
			logger.Error("Error closing database connection pool:", err)
			return fmt.Errorf("error closing database: %w", err)
		}
		logger.Info("Database connection pool closed successfully")
		return nil
	case <-ctx.Done():
		return fmt.Errorf("timeout waiting for database connections to close")
	}
}
