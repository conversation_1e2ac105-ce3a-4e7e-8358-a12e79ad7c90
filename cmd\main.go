package main

import (
	"context"
	"fastbird-dns/infra/logger"
	"fastbird-dns/infra/mysql"
	"fastbird-dns/internal/global"
	"fastbird-dns/internal/service"
	"flag"
	"os"
	"os/signal"
	"runtime/debug"
	"syscall"
	"time"
)

var (
	// 日志配置
	logPath = flag.String("log-path", "./logs", "日志文件路径, 示例: --log-path=/var/logs/fastbird")
	// MySQL配置
	mysqlHost = flag.String("mysql-host", "coder.53site.com", "MySQL主机地址, 示例: --mysql-host=127.0.0.1")
	mysqlPort = flag.Int("mysql-port", 3306, "MySQL端口, 示例: --mysql-port=3306")
	mysqlUser = flag.String("mysql-user", "go_wf_activity_api", "MySQL用户名, 示例: --mysql-user=fastbird")
	mysqlPass = flag.String("mysql-pass", "Mangosteen0!", "MySQL密码, 示例: --mysql-pass=password123")
	mysqlDB   = flag.String("mysql-db", "vpn-cloud", "MySQL数据库名, 示例: --mysql-db=fastbird_dns")
	// 环境配置
	env = flag.String("env", "dev", "运行环境(dev/prod), 示例: --env=prod")

	// 使用示例:
	// go run cmd/main.go --mysql-host=127.0.0.1 --mysql-port=3306 --mysql-user=fastbird --mysql-pass=password123 --mysql-db=fastbird_dns --env=prod
	// 或者使用默认配置:
	// go run cmd/main.go
)

func main() {
	// 添加 panic 恢复
	defer func() {
		if r := recover(); r != nil {
			logger.Error("Recovered from panic:", r)
			logger.Error("Stack trace:", string(debug.Stack()))
			os.Exit(1)
		}
	}()

	flag.Parse()
	global.GlobalAppInfo.Env = *env

	// 初始化日志
	err := logger.InitLogger(*logPath, "fastbird-dns", *env)
	if err != nil {
		panic("init logger failed: " + err.Error())
	}
	logger.Info("------ init ------")
	logger.Info("------ logger success ")
	// 打印flag获得的相关信息
	logger.Info("Flags configuration:")
	logger.Info("-- Log path:", *logPath)
	logger.Info("-- MySQL host:", *mysqlHost)
	logger.Info("-- MySQL port:", *mysqlPort)
	logger.Info("-- MySQL user:", *mysqlUser)
	logger.Info("-- MySQL database:", *mysqlDB)
	logger.Info("-- Env:", global.GlobalAppInfo.Env)
	logger.Info("------ init mysql start ")
	// 初始化数据库
	err = mysql.InitDB(&mysql.Config{
		Host:         *mysqlHost,
		Port:         *mysqlPort,
		User:         *mysqlUser,
		Password:     *mysqlPass,
		Database:     *mysqlDB,
		MaxIdleConns: 4, // 最大空闲连接数
		MaxOpenConns: 8, // 最大打开连接数
	})
	if err != nil {
		logger.Error("init mysql failed:", err)
		panic(err)
	}
	logger.Info("------ init mysql success! ------")

	dnsService := service.NewDnsService()

	// 创建一个上下文用于控制程序退出
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 设置信号处理
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT) // 监听Ctrl+C、kill、kill -9

	// 主循环
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()

	// 定时上报ping
	pingTicker := time.NewTicker(20 * time.Second)
	defer pingTicker.Stop()

	logger.Info("Service started successfully")

	go dnsService.RunScheduledTask()

	for {
		select {
		case <-ticker.C:
			go dnsService.RunScheduledTask()
		case <-pingTicker.C:
			go dnsService.Ping()
		case sig := <-signalChan:
			logger.Info("Received signal:", sig)
			logger.Info("Shutting down gracefully...")

			// 执行清理操作
			if err := mysql.CloseDB(); err != nil {
				logger.Error("Failed to close MySQL connection:", err)
			}
			logger.Info("Cleanup completed")
			return
		case <-ctx.Done():
			logger.Info("Context cancelled, shutting down...")
			return
		}
	}
}
