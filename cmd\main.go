package main

import (
	"context"
	"fastbird-dns/infra/logger"
	"fastbird-dns/infra/mysql"
	"fastbird-dns/internal/service"
	"flag"
	"os"
	"os/signal"
	"runtime/debug"
	"syscall"
	"time"
)

var (
	logPath    = flag.String("log-path", "./logs", "日志文件路径")
	mysqlHost  = flag.String("mysql-host", "coder.53site.com", "MySQL主机地址")
	mysqlPort  = flag.Int("mysql-port", 3306, "MySQL端口")
	mysqlUser  = flag.String("mysql-user", "go_wf_activity_api", "MySQL用户名")
	mysqlPass  = flag.String("mysql-pass", "Mangosteen0!", "MySQL密码")
	mysqlDB    = flag.String("mysql-db", "vpn-cloud", "MySQL数据库名")
)


func main() {
	// 添加 panic 恢复
	defer func() {
		if r := recover(); r != nil {
			logger.Error("Recovered from panic:", r)
			logger.Error("Stack trace:", string(debug.Stack()))
			os.Exit(1)
		}
	}()

	flag.Parse()

	// 初始化日志
	err := logger.InitLogger(*logPath, "fastbird-dns")
	if err != nil {
		panic("init logger failed: " + err.Error())
	}
	logger.Info("------ init ------")
	logger.Info("------ logger success ")
	logger.Info("------ init mysql start ")
	// 初始化数据库
	err = mysql.InitDB(&mysql.Config{
		Host:         *mysqlHost,
		Port:         *mysqlPort,
		User:         *mysqlUser,
		Password:     *mysqlPass,
		Database:     *mysqlDB,
		MaxIdleConns: 4, // 最大空闲连接数
		MaxOpenConns: 8, // 最大打开连接数
	})
	if err != nil {
		logger.Error("init mysql failed:", err)
		panic(err)
	}
	logger.Info("------ init mysql success! ------")

	dnsService := service.NewDnsService()

	// 创建一个上下文用于控制程序退出
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 设置信号处理
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT) // 监听Ctrl+C、kill、kill -9

	// 主循环
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()

	// 定时上报ping 
	pingTicker := time.NewTicker(20 * time.Second)
	defer pingTicker.Stop()

	logger.Info("Service started successfully")

	go dnsService.RunScheduledTask()

	for {
		select {
		case <-ticker.C:
			go dnsService.RunScheduledTask()
		case <-pingTicker.C:
			go dnsService.Ping()
		case sig := <-signalChan:
			logger.Info("Received signal:", sig)
			logger.Info("Shutting down gracefully...")
			
			// 执行清理操作
			if err := mysql.CloseDB(); err != nil {
				logger.Error("Failed to close MySQL connection:", err)
			}
			logger.Info("Cleanup completed")
			return
		case <-ctx.Done():
			logger.Info("Context cancelled, shutting down...")
			return
		}
	}
} 