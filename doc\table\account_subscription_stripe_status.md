## 表: account_subscription_stripe_status

```sql
CREATE TABLE `account_subscription_stripe_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `stripe_customer_id` varchar(128) DEFAULT NULL COMMENT 'stripe平台关联customer_id',
  `stripe_subscription_id` varchar(128) DEFAULT NULL COMMENT 'stripe平台订阅id',
  `stripe_subscription_status` varchar(64) DEFAULT 'incomplete' COMMENT 'status 字段可能的取值有：incomplete（未完成）、incomplete_expired（未完成过期）、trialing（试用中）、active（活跃）、past_due（逾期）、canceled（已取消）、unpaid（未支付）和 paused（暂停）。',
  `clientSecret` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `account_idx` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4
```

