## 表: game_v2ray_domain_whitelist

```sql
CREATE TABLE `game_v2ray_domain_whitelist` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `protocol` varchar(32) DEFAULT NULL,
  `domain` varchar(255) DEFAULT NULL,
  `game_region_id` bigint(20) DEFAULT NULL,
  `log_time` datetime DEFAULT NULL,
  `ping_delay_ms` double DEFAULT NULL,
  `status` int(11) DEFAULT '0',
  `traffic_bytes` bigint(20) DEFAULT '0',
  `traffic_bytes_temp` bigint(20) DEFAULT '0',
  `ip_mask` varchar(32) DEFAULT NULL,
  `vpn_region` varchar(64) DEFAULT NULL,
  `is_black` int(11) DEFAULT '0' COMMENT '0-正常 1-在黑',
  `is_manual` int(11) DEFAULT '0' COMMENT '0-自动 1-人工',
  `channel` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniidx` (`game_id`,`protocol`,`domain`,`game_region_id`,`channel`) USING BTREE,
  KEY `domain_idx` (`domain`) USING BTREE,
  KEY `ipmask_idx` (`ip_mask`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=803959 DEFAULT CHARSET=utf8mb4
```

