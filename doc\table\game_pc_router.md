## 表: game_pc_router

```sql
CREATE TABLE `game_pc_router` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `ip_mask` varchar(64) DEFAULT NULL,
  `channel` int(11) DEFAULT NULL COMMENT '1-UU',
  `type` int(11) DEFAULT NULL COMMENT '1-本机路由 2-vpn加速器路由',
  `metric` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `protocol` varchar(32) DEFAULT NULL,
  `country_code` varchar(32) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip_mask` (`user_id`,`ip_mask`) USING BTREE,
  UNIQUE KEY `ip_mask_2` (`game_id`,`game_region_id`,`ip_mask`) USING BTREE,
  KEY `ip_mask_3` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=75296215 DEFAULT CHARSET=utf8mb4
```

