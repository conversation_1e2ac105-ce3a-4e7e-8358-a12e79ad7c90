## 表: admin_user_operate_record

```sql
CREATE TABLE `admin_user_operate_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `admin_user_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `account_user_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `opt` tinyint(4) DEFAULT NULL COMMENT '1.踢人 2.封号',
  `remark` text COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `delsign` tinyint(4) DEFAULT NULL COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='用户处理记录表'
```

