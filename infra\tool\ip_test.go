package tool

import (
	"net"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestGetLocalIP(t *testing.T) {
	ip, err := GetLocalIP()
	
	// 检查是否有错误
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetLocalIP() returned error: %v", err)
	}
	
	// 检查返回的IP是否为空
	if ip == "" {
		t.Log("GetLocalIP() returned empty string, which might be valid in some environments")
		return
	}
	
	// 验证返回的是有效的IPv4地址
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		t.<PERSON><PERSON><PERSON>("GetLocalIP() returned invalid IP: %s", ip)
	}
	
	// 确保是IPv4地址
	if parsedIP.To4() == nil {
		t.<PERSON><PERSON>rf("GetLocalIP() returned non-IPv4 address: %s", ip)
	}
	
	t.Logf("Local IP: %s", ip)
}

func TestGetPublicIP(t *testing.T) {
	// 创建一个模拟的HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("*******"))
	}))
	defer server.Close()
	
	// 保存原始的IP服务列表
	originalIPServices := ipServices
	// 测试完成后恢复
	defer func() { ipServices = originalIPServices }()
	
	// 替换为我们的测试服务器
	ipServices = []string{server.URL}
	
	ip, err := GetPublicIP()
	
	// 检查是否有错误
	if err != nil {
		t.Errorf("GetPublicIP() returned error: %v", err)
	}
	
	// 检查返回的IP是否正确
	if ip != "*******" {
		t.Errorf("GetPublicIP() = %s; want *******", ip)
	}
	
	// 测试错误情况
	// 关闭服务器
	server.Close()
	
	// 应该返回错误
	ip, err = GetPublicIP()
	if err == nil {
		t.Errorf("GetPublicIP() should return error when all services fail")
	}
	
	if ip != "" {
		t.Errorf("GetPublicIP() should return empty string when all services fail, got: %s", ip)
	}
}

func TestGetIPInfo(t *testing.T) {
	// 创建一个模拟的HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("*******"))
	}))
	defer server.Close()
	
	// 保存原始的IP服务列表
	originalIPServices := ipServices
	// 测试完成后恢复
	defer func() { ipServices = originalIPServices }()
	
	// 替换为我们的测试服务器
	ipServices = []string{server.URL}
	
	localIP, publicIP := GetIPInfo()
	
	// 本地IP可能为空，这取决于测试环境
	t.Logf("Local IP: %s", localIP)
	
	// 检查公网IP是否正确
	if publicIP != "*******" {
		t.Errorf("GetIPInfo() returned publicIP = %s; want *******", publicIP)
	}
}

// 为了使测试可以访问ipServices变量，需要将其导出或在测试中定义
var ipServices = []string{
	"https://api.ipify.org",
	"https://ifconfig.me/ip",
	"https://icanhazip.com",
} 