package service

import (
	"fastbird-dns/infra/logger"
	"fastbird-dns/internal/model"
	"fastbird-dns/internal/vo"
	"time"
)


// GetBlacklistDomains 获取黑名单域名
func (s *DnsService) GetBlacklistDomains(innerIP string, pageSize int) {
	logger.Info("***** Getting blacklist domains for IP:", innerIP, " pageSize:", pageSize)

	regionIdStr, err := s.getRegionIdStrs(innerIP)
	if err != nil {
		logger.Error("error:", err)
		return
	}

	gameList := s.dnsDao.GetBlackGameList(regionIdStr)
	logger.Info("Blacklist game  count: ", len(gameList))

	// 遍历游戏列表
	for index, game := range gameList {
		// 获取游戏对应的黑名单域名列表
		domainList := s.dnsDao.GetBlcaktDomainList(int(game.ID))
		if len(domainList) == 0 {
			logger.Info("[", index, "] No blacklist domains found for game: ", game.ID)
			continue
		}
		logger.Info("[", index, "] Game:", game.ID, " got ", len(domainList), " blacklist domains")
		// 查询游戏对应的区域
		regionList := s.dnsDao.GetGameRegionId(int(game.ID))
		if len(regionList) == 0 {
			logger.Info("[", index, "] No region found for game: ", game.ID)
			continue
		}
		// 解析域名
		for _, domain := range domainList {
			ipv4s, err := s.ResolveDomain(index, domain.DomainName)
			if err != nil {
				continue
			}
			s.dnsDao.InsertBlackDomainIpResolve(domain.DomainName, ipv4s, regionList)
			time.Sleep(500 * time.Millisecond)
		}
	}

	logger.Info("***** Blacklist domains resolve success! *****")
}

// GetCommonBlackDomainList 获取通用黑名单域名
func (s *DnsService) GetCommonBlackDomainList(innerIP string) {
	logger.Info("***** Getting common blacklist domains for IP: ", innerIP)
	domainList := s.dnsDao.GetCommonBlackDomainList()
	if len(domainList) == 0 {
		logger.Warn("common blcklist is empty!")
		return
	}
	
	var requests []vo.InsertDomainIpResolveRequest

	for index, domain := range domainList {
		ipv4s, err := s.ResolveDomain(index, domain.DomainName)
		if err != nil {
			continue
		}
		
		requests = append(requests, vo.InsertDomainIpResolveRequest{
			DomainName:   domain.DomainName,
			Ipv4s:        ipv4s,
			GameRegionId: domain.GameRegionID,
		})

		// When batch size is reached or it's the last item
		if len(requests) >= batchSize || index == len(domainList)-1 {
			if len(requests) > 0 {
				s.dnsDao.InsertDomainIpResolve(requests)
				requests = requests[:0] // Clear slice but keep capacity
			}
			time.Sleep(500 * time.Millisecond)
		}
	}
	logger.Info("***** Common Blacklist domains resolve success! *****")
}