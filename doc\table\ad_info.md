## 表: ad_info

```sql
CREATE TABLE `ad_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL COMMENT '广告名称',
  `system_channel` int(11) DEFAULT NULL COMMENT '1 苹果 2 安卓 3 pc 4 鸿蒙\n',
  `loading_image` varchar(200) NOT NULL COMMENT '开屏图片',
  `tend_url` text NOT NULL COMMENT '开屏点击后的跳转地址',
  `ad_time` int(11) NOT NULL DEFAULT '5' COMMENT '广告显示时间 单位（s）',
  `type` int(11) DEFAULT NULL COMMENT '0: 开屏 1: 个人页banner 2: pc广告',
  `publish_time` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `delsign` tinyint(1) DEFAULT '0' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4
```

