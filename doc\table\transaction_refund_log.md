## 表: transaction_refund_log

```sql
CREATE TABLE `transaction_refund_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `transaction_id` bigint(20) NOT NULL COMMENT '关联的订单ID',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `refund_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '退款时间',
  `reason` text COMMENT '退款原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `order_id` (`transaction_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4
```

