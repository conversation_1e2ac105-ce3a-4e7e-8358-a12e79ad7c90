## 表: router_server_region_copy

```sql
CREATE TABLE `router_server_region_copy` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `region_name` varchar(50) DEFAULT NULL,
  `show_name` varchar(255) DEFAULT NULL COMMENT '线路名称',
  `longitude` double(16,4) DEFAULT NULL COMMENT '经度',
  `latitude` double(16,4) DEFAULT NULL COMMENT '纬度',
  `ecs_type` int(11) NOT NULL COMMENT '运营商 1:华为 2:阿里',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` double(4,0) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2000126003 DEFAULT CHARSET=utf8mb4 COMMENT='ECS区域表'
```

