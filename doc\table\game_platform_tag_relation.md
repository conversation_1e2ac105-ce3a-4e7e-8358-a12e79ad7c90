## 表: game_platform_tag_relation

```sql
CREATE TABLE `game_platform_tag_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_platform_id` bigint(20) unsigned NOT NULL,
  `tag_id` bigint(20) unsigned NOT NULL,
  `delsign` tinyint(1) DEFAULT '0' COMMENT '删除标志位0有效1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_platform_id`,`tag_id`) USING BTREE,
  KEY `idx_tag_id` (`tag_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=90227 DEFAULT CHARSET=utf8mb4 COMMENT='爬取游戏标签（区分平台）关联表'
```

