## 表: account_subscription_record

```sql
CREATE TABLE `account_subscription_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `membership_id` int(11) DEFAULT NULL,
  `record_id` bigint(20) DEFAULT NULL,
  `transaction_id` bigint(20) DEFAULT NULL,
  `subscription_type` int(11) DEFAULT NULL COMMENT '0 免费 1 订阅 2 续订 3 内购 4 订阅升级 5 订阅降级 6 退款',
  `add_seconds` int(11) DEFAULT NULL,
  `end_time` datetime NOT NULL COMMENT '截止时间',
  `channel` int(11) DEFAULT NULL COMMENT '0: 苹果 1:paypal 2:google 3 支付宝 4:签到 5兑换码 6:邀请码 7 微信',
  `create_date` date DEFAULT NULL,
  `create_time` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=723 DEFAULT CHARSET=utf8mb4
```

