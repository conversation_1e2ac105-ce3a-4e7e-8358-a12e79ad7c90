## 表: router_server_dns

```sql
CREATE TABLE `router_server_dns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `public_ip` varchar(255) NOT NULL,
  `inner_ip` varchar(255) NOT NULL,
  `dns_server_ip` varchar(255) NOT NULL,
  `status` int(11) NOT NULL COMMENT '1-正常 0-不检测 -1-故障',
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  `last_error_time` datetime DEFAULT NULL COMMENT '最后一次检查挂掉时间',
  `last_check_time` datetime DEFAULT NULL COMMENT '最后一次检查时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4
```

