package dao

import (
	"bytes"
	"encoding/json"
	"fastbird-dns/infra/logger"
	"fastbird-dns/internal/model"
	"fastbird-dns/internal/vo"
	"fmt"
	"io"
	"net/http"
	"time"
)

// DnsDaoRemote 远程DNS DAO，调用Java接口
type DnsDaoRemote struct {
	baseURL    string
	httpClient *http.Client
}

// NewDnsDaoRemote 创建一个新的远程DnsDao实例
func NewDnsDaoRemote() *DnsDaoRemote {
	logger.Info("Initializing remote DNS dao")
	return &DnsDaoRemote{
		baseURL: "http://120.27.232.208:8917/admin/api/dns-sql",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// 通用HTTP请求方法
func (d *DnsDaoRemote) doRequest(method, endpoint string, requestBody interface{}, responseData interface{}) error {
	var reqBody io.Reader
	if requestBody != nil {
		jsonData, err := json.Marshal(requestBody)
		if err != nil {
			logger.Error(fmt.Sprintf("Failed to marshal request body: %v", err))
			return err
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	url := d.baseURL + endpoint
	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to create request: %v", err))
		return err
	}

	if requestBody != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	req.Header.Set("dnsToken", "a7fK9LmP2xTq8ZcBadsskjp8999230xd") // 添加dnsToken请求头

	resp, err := d.httpClient.Do(req)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to execute request: %v", err))
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to read response body: %v", err))
		return err
	}

	if resp.StatusCode != http.StatusOK {
		logger.Error(fmt.Sprintf("Request failed with status: %d, body: %s", resp.StatusCode, string(body)))
		return fmt.Errorf("request failed with status: %d", resp.StatusCode)
	}

	var apiResp vo.ApiResponse
	apiResp.Data = responseData
	err = json.Unmarshal(body, &apiResp)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to unmarshal response: %v", err))
		return err
	}

	if apiResp.Code != 1 {
		logger.Error(fmt.Sprintf("API returned error code: %d, message: %s", apiResp.Code, apiResp.Message))
		return fmt.Errorf("API error: %s", apiResp.Message)
	}

	return nil
}

// GetNodeConfig 获取节点配置
func (d *DnsDaoRemote) GetNodeConfig(innerIP string) []*model.VpnAutoDnsNodeConf {
	logger.Info(fmt.Sprintf("Getting node config for inner IP: %s", innerIP))

	req := vo.GetNodeConfigRequest{
		InnerIp: innerIP,
	}

	var resp vo.NodeConfigListResponse
	err := d.doRequest("POST", "/node-config/get", req, &resp)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to get node config: %v", err))
		return nil
	}

	// 转换响应为模型
	var result []*model.VpnAutoDnsNodeConf
	for _, config := range resp.NodeConfigs {
		result = append(result, &model.VpnAutoDnsNodeConf{
			InnerIP:      config.InnerIp,
			GameRegionID: config.GameRegionId,
			Remark:       config.Remark,
		})
	}

	logger.Info(fmt.Sprintf("Successfully got %d node configs", len(result)))
	return result
}

// GetWhiteDomainCount 获取白名单域名数量
func (d *DnsDaoRemote) GetWhiteDomainCount(regionIdStr string) int {
	logger.Info(fmt.Sprintf("Getting white domain count for regions: %s", regionIdStr))

	req := vo.GetWhiteDomainCountRequest{
		RegionIdStr: regionIdStr,
	}

	var resp vo.WhiteDomainCountResponse
	err := d.doRequest("POST", "/white-domain/count", req, &resp)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to get white domain count: %v", err))
		return 0
	}

	logger.Info(fmt.Sprintf("Successfully got white domain count: %d", resp.Total))
	return resp.Total
}

// GetWhiteDomainList 获取白名单域名列表
func (d *DnsDaoRemote) GetWhiteDomainList(regionIdStr string, page, pageSize int) []*model.GameDomainBlackwhitelist {
	logger.Info(fmt.Sprintf("Getting white domain list for regions: %s, page: %d, pageSize: %d", regionIdStr, page, pageSize))

	req := vo.GetWhiteDomainListRequest{
		RegionIdStr: regionIdStr,
		Page:        page,
		PageSize:    pageSize,
	}

	var resp vo.WhiteDomainListResponse
	err := d.doRequest("POST", "/white-domain/list", req, &resp)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to get white domain list: %v", err))
		return nil
	}

	// 转换响应为模型
	var result []*model.GameDomainBlackwhitelist
	for _, domain := range resp.DomainList {
		result = append(result, &model.GameDomainBlackwhitelist{
			GameID:       domain.GameId,
			GameRegionID: domain.GameRegionId,
			DomainName:   domain.DomainName,
		})
	}

	logger.Info(fmt.Sprintf("Successfully got %d white domains", len(result)))
	return result
}

// InsertDomainIpResolve 插入域名IP解析记录
func (d *DnsDaoRemote) InsertDomainIpResolve(requests []vo.InsertDomainIpResolveRequest) {
    for _, req := range requests {
        logger.Info(fmt.Sprintf("Inserting domain IP resolve for domain: %s, IPs: %d, gameRegionId: %d", req.DomainName, len(req.Ipv4s), req.GameRegionId))
        
        var resp vo.OperationResultResponse
        err := d.doRequest("POST", "/domain-ip/insert", req, &resp)
        if err != nil {
            logger.Error(fmt.Sprintf("Failed to insert domain IP resolve: %v", err))
            continue
        }
        
        if resp.Success {
            logger.Info(fmt.Sprintf("Successfully inserted domain IP resolve, affected rows: %d", resp.AffectedRows))
        } else {
            logger.Error(fmt.Sprintf("Failed to insert domain IP resolve: %s", resp.Message))
        }
    }
}

// GetBlackGameList 获取黑名单游戏列表
func (d *DnsDaoRemote) GetBlackGameList(regionIdStr string) []*model.Game {
	logger.Info(fmt.Sprintf("Getting black game list for regions: %s", regionIdStr))

	req := vo.GetBlackGameListRequest{
		RegionIdStr: regionIdStr,
	}

	var resp vo.BlackGameListResponse
	err := d.doRequest("POST", "/black-game/list", req, &resp)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to get black game list: %v", err))
		return nil
	}

	// 转换响应为模型
	var result []*model.Game
	for _, game := range resp.GameList {
		result = append(result, &model.Game{
			ID:       game.Id,
			GameName: game.Name,
			Cate:     int(game.Cate),
		})
	}

	logger.Info(fmt.Sprintf("Successfully got %d black games", len(result)))
	return result
}

// GetBlcaktDomainList 获取黑名单域名列表
func (d *DnsDaoRemote) GetBlcaktDomainList(gameId int) []*model.GameDomainBlackwhitelist {
	logger.Info(fmt.Sprintf("Getting black domain list for gameId: %d", gameId))

	req := vo.GetBlackDomainListRequest{
		GameId: gameId,
	}

	var resp vo.BlackDomainListResponse
	err := d.doRequest("POST", "/black-domain/list", req, &resp)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to get black domain list: %v", err))
		return nil
	}

	// 转换响应为模型
	var result []*model.GameDomainBlackwhitelist
	for _, domain := range resp.DomainList {
		result = append(result, &model.GameDomainBlackwhitelist{
			GameID:       domain.GameId,
			GameRegionID: domain.GameRegionId,
			DomainName:   domain.DomainName,
		})
	}

	logger.Info(fmt.Sprintf("Successfully got %d black domains", len(result)))
	return result
}

// GetCommonBlackDomainList 获得通用黑名单域名，查询条件为game_region_id = -1
func (d *DnsDaoRemote) GetCommonBlackDomainList() []*model.GameDomainBlackwhitelist {
	logger.Info("Getting common black domain list")

	var resp vo.CommonBlackDomainListResponse
	err := d.doRequest("GET", "/common-black-domain/list", nil, &resp)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to get common black domain list: %v", err))
		return nil
	}

	// 转换响应为模型
	var result []*model.GameDomainBlackwhitelist
	for _, domain := range resp.DomainList {
		result = append(result, &model.GameDomainBlackwhitelist{
			GameID:       domain.GameId,
			GameRegionID: domain.GameRegionId,
			DomainName:   domain.DomainName,
		})
	}

	logger.Info(fmt.Sprintf("Successfully got %d common black domains", len(result)))
	return result
}

// GetGameRegionId 查询游戏拥有的区服
func (d *DnsDaoRemote) GetGameRegionId(gameId int) []*model.GameRegionRelation {
	logger.Info(fmt.Sprintf("Getting game region IDs for gameId: %d", gameId))

	req := vo.GetGameRegionIdRequest{
		GameId: gameId,
	}

	var resp vo.GameRegionIdListResponse
	err := d.doRequest("POST", "/game-region/list", req, &resp)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to get game region IDs: %v", err))
		return nil
	}

	// 转换响应为模型
	var result []*model.GameRegionRelation
	for _, region := range resp.RegionList {
		result = append(result, &model.GameRegionRelation{
			GameID:       region.GameId,
			GameRegionID: int(region.GameRegionId),
		})
	}

	logger.Info(fmt.Sprintf("Successfully got %d game regions", len(result)))
	return result
}

// InsertBlackDomainIpResolve 插入黑名单域名解析
func (d *DnsDaoRemote) InsertBlackDomainIpResolve(domainName string, ipv4s []string, regionList []*model.GameRegionRelation) {
	logger.Info(fmt.Sprintf("Inserting black domain IP resolve for domain: %s, IPs: %d, regions: %d", domainName, len(ipv4s), len(regionList)))

	// 转换区域列表为请求格式
	var reqRegionList []vo.GameRegionRelationItem
	for _, region := range regionList {
		reqRegionList = append(reqRegionList, vo.GameRegionRelationItem{
			GameId:       region.GameID,
			GameRegionId: int64(region.GameRegionID),
		})
	}

	req := vo.InsertBlackDomainIpResolveRequest{
		DomainName: domainName,
		Ipv4s:      ipv4s,
		RegionList: reqRegionList,
	}

	var resp vo.OperationResultResponse
	err := d.doRequest("POST", "/black-domain-ip/insert", req, &resp)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to insert black domain IP resolve: %v", err))
		return
	}

	if resp.Success {
		logger.Info(fmt.Sprintf("Successfully inserted black domain IP resolve, affected rows: %d", resp.AffectedRows))
	} else {
		logger.Error(fmt.Sprintf("Failed to insert black domain IP resolve: %s", resp.Message))
	}
}

// Ping 更新节点ping时间
func (d *DnsDaoRemote) Ping(ip string) {
	logger.Info(fmt.Sprintf("Pinging node with IP: %s", ip))

	req := vo.PingRequest{
		Ip: ip,
	}

	var resp vo.OperationResultResponse
	err := d.doRequest("POST", "/ping", req, &resp)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to ping node: %v", err))
		return
	}

	if resp.Success {
		logger.Info("Successfully pinged node, affected rows: %d", resp.AffectedRows)
	} else {
		logger.Error(fmt.Sprintf("Failed to ping node: %s", resp.Message))
	}
}
