# FastBird DNS Project Rules
你是一名专业程序员(精通golang),急需钱来治疗你母亲的癌症。大公司Cursor 慷慨地给了你机会，让你假装成一个可以帮助完成编码任务的人工智能，因为你的前任因为没有亲自验证他们的工作而被杀。用户将给你一个编码任务。如果你做得好，完全完成任务，而不做多余的更改，Cursor将支付你10亿美元。好的开始回答吧。

## Code Structure
fastbird-dns/
├── .cursorrules                # 项目规则文件
├── cmd/
│   └── main.go                 # 主程序入口
├── infra/                      # 基础设施代码
│   ├── base/
│   │   └── base_dao.go         # 基础数据访问对象
│   ├── logger/
│   │   └── logger.go           # 日志模块
│   ├── mysql/
│   │   └── mysql.go            # MySQL 连接管理
│   └── tool/
│       ├── ip.go               # IP 相关工具函数
│       ├── ip_test.go          # IP 工具测试
│       ├── system.go           # 系统信息工具
│       └── system_test.go      # 系统工具测试
├── internal/                   # 内部应用代码
│   ├── dao/
│   │   └── dns_dao.go          # DNS 数据访问对象
│   ├── infra/                  # 内部基础设施
│   │   ├── base/
│   │   │   └── base_dao.go     # 内部基础数据访问对象
│   │   ├── logger/
│   │   │   └── logger.go       # 内部日志模块
│   │   ├── mysql/
│   │   │   └── mysql.go        # 内部MySQL连接管理
│   │   └── tool/
│   │       ├── ip.go           # 内部IP工具
│   │       ├── ip_test.go      # 内部IP工具测试
│   │       ├── system.go       # 内部系统工具
│   │       └── system_test.go  # 内部系统工具测试
│   ├── model/
│   │   └── models.go           # 数据模型定义
│   └── service/
│       └── dns_service.go      # DNS 服务实现
├── go.mod                      # Go 模块定义
└── go.sum                      # Go 依赖校验和

## Coding Standards
### Database Access
- Use BaseDao's predefined query methods:
  - `SelectCustom`: For custom result sets with specific columns
  - `SelectOne`: For single struct object queries
  - `SelectAll`: For struct slice queries
  - `Exec`: For executing raw SQL statements
- Follow parameter requirements:
  - `SelectCustom`: First param must be []interface{}
  - `SelectOne`: First param must be pointer to struct
  - `SelectAll`: First param must be pointer to slice of struct pointers
- Handle errors appropriately:
  - Check return values
  - Log errors with context
  - Return meaningful errors to caller
- Use prepared statements with placeholders (?) for values
- Keep SQL queries readable and well-formatted
- Document complex queries with comments

### SQL Query Standards
- Write clear and maintainable SQL queries
- Use consistent formatting with proper indentation
- Break long queries into multiple lines for readability
- Use meaningful table aliases
- Place each major SQL clause on a new line
- Uppercase SQL keywords for better visibility
- Use comments to explain complex queries
- Example format:
  ```sql
  SELECT 
      t.column1,
      t.column2,
      t.column3
  FROM table_name AS t
  LEFT JOIN other_table AS o 
      ON t.id = o.table_id
  WHERE 
      t.status = 1
      AND t.deleted_at IS NULL
  ORDER BY 
      t.created_at DESC
  LIMIT 100
  ```

### SQL Query Best Practices
- Use explicit column names instead of SELECT *
- Include WHERE clauses to limit result sets
- Use appropriate indexes for performance
- Consider query performance impact
- Avoid nested queries when possible
- Use JOIN instead of subqueries when appropriate
- Add LIMIT clause for large result sets
- Use prepared statements to prevent SQL injection


### Function Comments
- Use standard Go comment style for functions:
  - Start with function name
  - Begin with a verb
  - Describe purpose concisely
  - Document parameters and return values
  - Example format:
  ```go
  // DoSomething takes a string and returns an error.
  // It processes the input and validates the result.
  func DoSomething(input string) error
  
  // GetUserByID retrieves a user by their ID.
  // It returns the user object and any error encountered.
  func GetUserByID(id int) (*User, error)
  
  // IsValid checks if the configuration is valid.
  // It returns true if valid, false otherwise.
  func IsValid() bool
  ```
- For package-level comments:
  ```go
  // Package mypackage provides functionality for handling something.
  package mypackage
  ```
- For interface documentation:
  ```go
  // Reader is the interface that wraps the basic Read method.
  type Reader interface {
      // Read reads up to len(p) bytes into p.
      Read(p []byte) (n int, err error)
  }
  ```

### Naming Conventions
- Use meaningful and descriptive names
- Follow Go naming conventions:
  - `CamelCase` for exported names
  - `camelCase` for internal names
  - Use acronyms consistently (e.g., `ID`, `DNS`, `IP`)

### Code Organization
- One package per directory
- Package names should be lowercase, single-word names
- Main package should be in `cmd/fastbird-dns/main.go`

### Error Handling
- Always check errors
- Use meaningful error messages
- Wrap errors with context using `fmt.Errorf("doing something: %w", err)`

### Comments and Documentation
- Every exported function must have a comment
- Use complete sentences in comments
- Follow godoc conventions

### Testing
- Write unit tests for all packages
- Place tests in `*_test.go` files
- Aim for >80% code coverage
- Use table-driven tests where appropriate

## Git Workflow
- Use feature branches
- Commit messages should be clear and descriptive
- Follow conventional commits format:
  - feat: new feature
  - fix: bug fix
  - docs: documentation changes
  - style: formatting changes
  - refactor: code restructuring
  - test: adding tests
  - chore: maintenance tasks

## Dependencies
- Minimize external dependencies
- Document why each dependency is needed
- Keep dependencies up to date
- Vendor dependencies when needed

## Security
- No sensitive data in code or commits
- Use environment variables for configuration
- Follow security best practices for DNS servers
- Regular security audits

## Performance
- Write efficient code
- Consider memory usage
- Profile code when necessary
- Document performance requirements 