## 表: game_account_relation

```sql
CREATE TABLE `game_account_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) unsigned NOT NULL COMMENT '用户id',
  `game_id` bigint(20) unsigned NOT NULL COMMENT '游戏id',
  `region_id` int(11) DEFAULT NULL COMMENT '游戏服务器区域id',
  `is_connecting` tinyint(4) DEFAULT NULL COMMENT '是否正在连接 0.未连接 1.正在连接',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_id`,`account_id`) USING BTREE,
  KEY `idx_tag_id` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13274 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签关联表'
```

