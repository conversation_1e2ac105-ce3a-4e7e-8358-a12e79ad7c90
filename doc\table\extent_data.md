## 表: extent_data

```sql
CREATE TABLE `extent_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `oaid` varchar(255) DEFAULT NULL,
  `imei` varchar(255) DEFAULT NULL,
  `callback` varchar(255) DEFAULT NULL,
  `task_id` varchar(255) DEFAULT NULL,
  `task_name` varchar(255) DEFAULT NULL,
  `action_type` varchar(255) DEFAULT NULL COMMENT 'IMP：对应精准曝光监测链接\nCLICK：对应点击上报监测链接\nDOWNLOAD：对应下载上报监测链接\nINSTALL：对应安装上报监测链接\nDEEPLINKCLICK：对应打开跳转deeplink上报监测链接\n',
  `id_type` varchar(255) NOT NULL COMMENT '0 IMEI',
  `delsign` int(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='华为推广返回oaid数据'
```

