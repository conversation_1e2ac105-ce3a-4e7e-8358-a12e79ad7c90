## 表: transaction_googleplay_log

```sql
CREATE TABLE `transaction_googleplay_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` varchar(128) DEFAULT NULL COMMENT 'googleplay的消息id',
  `transaction_id` bigint(20) DEFAULT NULL COMMENT 'transaction表id',
  `transaction_status` int(11) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `message_id_idx` (`message_id`) USING BTREE,
  KEY `idx_transaction_date` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=210 DEFAULT CHARSET=utf8mb4
```

