## 表: 旧_game_cn_ipv4_blacklist

```sql
CREATE TABLE `旧_game_cn_ipv4_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '游戏ID',
  `ip` bigint(20) NOT NULL COMMENT 'IP地址（整数形式）',
  `gateway` int(11) NOT NULL COMMENT '网关（24位掩码）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_ip_gat_unique` (`ip`,`gateway`,`game_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP黑名单表'
```

## 表: 旧_game_cn_ipv4_whitelist

```sql
CREATE TABLE `旧_game_cn_ipv4_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '游戏ID',
  `ip` bigint(20) NOT NULL COMMENT 'IP地址（整数形式）',
  `gateway` int(11) NOT NULL COMMENT '网关（24位掩码）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `game_region_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_ip_gat_unique` (`ip`,`gateway`,`game_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=91 DEFAULT CHARSET=utf8mb4 COMMENT='IP白名单表'
```

## 表: 旧_game_int_ipv4_blacklist

```sql
CREATE TABLE `旧_game_int_ipv4_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL COMMENT '游戏ID',
  `ip` bigint(20) NOT NULL COMMENT 'IP地址（整数形式）',
  `gateway` int(11) NOT NULL COMMENT '网关（24位掩码）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_ip_gat_unique` (`ip`,`gateway`,`game_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海外IP黑名单表'
```

## 表: 旧_game_int_ipv4_whitelist

```sql
CREATE TABLE `旧_game_int_ipv4_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL COMMENT '游戏ID',
  `ip` bigint(20) NOT NULL COMMENT 'IP地址（整数形式）',
  `gateway` int(11) NOT NULL COMMENT '网关（24位掩码）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `game_region_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_ip_gat_unique` (`ip`,`gateway`,`game_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海外IP白名单表'
```

## 表: 旧_nongame_cn_domain_temp

```sql
CREATE TABLE `旧_nongame_cn_domain_temp` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=66378 DEFAULT CHARSET=utf8mb4 COMMENT='国内(影音模式)域名临时表'
```

## 表: 旧_nongame_cn_domain_temp_result

```sql
CREATE TABLE `旧_nongame_cn_domain_temp_result` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `is_regex` tinyint(4) DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`domain_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国内(影音模式)域名临时表'
```

## 表: 旧_nongame_cn_domain_whitelist

```sql
CREATE TABLE `旧_nongame_cn_domain_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='非游戏类国内(影音)域名白名单表'
```

## 表: 旧_nongame_cn_domain_whitelist_regex

```sql
CREATE TABLE `旧_nongame_cn_domain_whitelist_regex` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `regex` varchar(255) DEFAULT NULL,
  `level` int(11) DEFAULT NULL,
  `level_desc_num` int(11) DEFAULT NULL,
  `level_asc_num` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `regex` (`regex`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
```

## 表: 旧_nongame_int_domain_temp

```sql
CREATE TABLE `旧_nongame_int_domain_temp` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3021239 DEFAULT CHARSET=utf8mb4 COMMENT='海外（学习模式）域名临时表'
```

## 表: 旧_nongame_int_domain_temp_result

```sql
CREATE TABLE `旧_nongame_int_domain_temp_result` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `is_regex` tinyint(4) DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=35986 DEFAULT CHARSET=utf8mb4 COMMENT='海外（学习模式）域名临时表'
```

## 表: 旧_nongame_int_domain_whitelist

```sql
CREATE TABLE `旧_nongame_int_domain_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=324 DEFAULT CHARSET=utf8mb4 COMMENT='海外非游戏（学习）域名白名单表'
```

## 表: 旧_nongame_int_domain_whitelist_regex

```sql
CREATE TABLE `旧_nongame_int_domain_whitelist_regex` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `regex` varchar(255) DEFAULT NULL,
  `level` int(11) DEFAULT NULL,
  `level_desc_num` int(11) DEFAULT NULL,
  `level_asc_num` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `regex` (`regex`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10951 DEFAULT CHARSET=utf8mb4
```

## 表: 老_game_cn_domain_blacklist

```sql
CREATE TABLE `老_game_cn_domain_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '游戏ID',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `game_region_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`game_id`,`game_region_id`,`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COMMENT='域名黑名单表'
```

## 表: 老_game_cn_domain_whitelist

```sql
CREATE TABLE `老_game_cn_domain_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '游戏ID',
  `game_region_id` int(11) DEFAULT NULL,
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '黑名单？',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`game_id`,`game_region_id`,`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10237 DEFAULT CHARSET=utf8mb4 COMMENT='域名白名单表'
```

## 表: 老_game_cn_ip_blacklist

```sql
CREATE TABLE `老_game_cn_ip_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '游戏ID',
  `game_region_id` int(11) DEFAULT NULL,
  `ip` varchar(255) NOT NULL COMMENT 'IP地址',
  `ip_mask` varchar(64) DEFAULT NULL COMMENT '带掩码的',
  `is_domain_reslove` varchar(255) DEFAULT NULL COMMENT '域名解析出来的',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_idx` (`game_id`,`game_region_id`,`ip`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=194 DEFAULT CHARSET=utf8mb4 COMMENT='游戏黑名单列表'
```

## 表: 老_game_cn_ip_blacklist_common

```sql
CREATE TABLE `老_game_cn_ip_blacklist_common` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `ip_mask` varchar(64) DEFAULT NULL COMMENT '带掩码的',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `domain_blacklist_id` int(11) DEFAULT NULL,
  `is_domain_reslove` varchar(255) DEFAULT NULL COMMENT '域名解析出来的',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_idx` (`domain_blacklist_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COMMENT='海外游戏ip黑名单通用表'
```

## 表: 老_game_cn_ip_whitelist

```sql
CREATE TABLE `老_game_cn_ip_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '游戏ID',
  `game_region_id` int(11) DEFAULT NULL,
  `ip` varchar(255) NOT NULL COMMENT 'IP地址',
  `ip_mask` varchar(64) DEFAULT NULL COMMENT '带掩码的',
  `is_domain_reslove` varchar(255) DEFAULT NULL COMMENT '域名解析出来的',
  `gateway` int(11) NOT NULL COMMENT '网关（24位掩码）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '黑名单？',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_idx` (`game_id`,`game_region_id`,`ip`) USING BTREE,
  KEY `ip_mask` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=66718 DEFAULT CHARSET=utf8mb4 COMMENT='IP白名单表'
```

## 表: 老_game_int_domain_blacklist

```sql
CREATE TABLE `老_game_int_domain_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL COMMENT '游戏ID',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `game_region_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`game_id`,`game_region_id`,`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=398 DEFAULT CHARSET=utf8mb4 COMMENT='海外域名黑名单表'
```

## 表: 老_game_int_domain_whitelist

```sql
CREATE TABLE `老_game_int_domain_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL COMMENT '游戏ID',
  `game_region_id` int(11) DEFAULT NULL,
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`game_id`,`game_region_id`,`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8802 DEFAULT CHARSET=utf8mb4 COMMENT='海外域名白名单表'
```

## 表: 老_game_int_ip_blacklist

```sql
CREATE TABLE `老_game_int_ip_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL COMMENT '游戏ID',
  `game_region_id` int(11) DEFAULT NULL,
  `ip` varchar(255) NOT NULL COMMENT 'IP地址',
  `ip_mask` varchar(64) DEFAULT NULL COMMENT '带掩码的',
  `is_domain_reslove` varchar(255) DEFAULT NULL COMMENT '域名解析出来的',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '黑名单？',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_idx` (`game_id`,`game_region_id`,`ip`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2631 DEFAULT CHARSET=utf8mb4 COMMENT='游戏黑名单列表'
```

## 表: 老_game_int_ip_blacklist_common

```sql
CREATE TABLE `老_game_int_ip_blacklist_common` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `ip_mask` varchar(64) DEFAULT NULL COMMENT '带掩码的',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `domain_blacklist_id` int(11) DEFAULT NULL,
  `is_domain_reslove` varchar(255) DEFAULT NULL COMMENT '域名解析出来的',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_idx` (`domain_blacklist_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='海外游戏ip黑名单通用表'
```

## 表: 老_game_int_ip_whitelist

```sql
CREATE TABLE `老_game_int_ip_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL COMMENT '游戏ID',
  `game_region_id` int(11) DEFAULT NULL,
  `ip` varchar(255) NOT NULL COMMENT 'IP地址',
  `ip_mask` varchar(64) DEFAULT NULL COMMENT '带掩码的',
  `is_domain_reslove` varchar(255) DEFAULT NULL COMMENT '域名解析出来的',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '黑名单？',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `game_idx` (`game_id`,`game_region_id`) USING BTREE,
  KEY `ip_mask` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=55970 DEFAULT CHARSET=utf8mb4 COMMENT='海外IP白名单表'
```

## 表: 老_nongame_cn_domain_whitelist

```sql
CREATE TABLE `老_nongame_cn_domain_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '黑名单？',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6748 DEFAULT CHARSET=utf8mb4 COMMENT='海外域名白名单表'
```

## 表: 老_nongame_cn_ip_whitelist

```sql
CREATE TABLE `老_nongame_cn_ip_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `ip` varchar(255) NOT NULL COMMENT 'IP地址',
  `ip_mask` varchar(64) DEFAULT NULL COMMENT '带掩码的',
  `is_domain_reslove` varchar(255) DEFAULT NULL COMMENT '域名解析出来的',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '黑名单？',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ip_uni` (`ip`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=35633 DEFAULT CHARSET=utf8mb4 COMMENT='海外IP白名单表'
```

## 表: 老_nongame_int_domain_whitelist

```sql
CREATE TABLE `老_nongame_int_domain_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '黑名单？',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`domain_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海外域名白名单表'
```

## 表: 老_nongame_int_ip_whitelist

```sql
CREATE TABLE `老_nongame_int_ip_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `ip` varchar(255) NOT NULL COMMENT 'IP地址',
  `ip_mask` varchar(64) DEFAULT NULL COMMENT '带掩码的',
  `is_domain_reslove` varchar(255) DEFAULT NULL COMMENT '域名解析出来的',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '黑名单？',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ip_uni` (`ip`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海外IP白名单表'
```

## 表: account

```sql
CREATE TABLE `account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `nick_name` varchar(255) DEFAULT NULL COMMENT '昵称',
  `user_name` varchar(255) DEFAULT NULL COMMENT '用户名',
  `external_user_id` bigint(20) DEFAULT NULL COMMENT '外显用户id',
  `password` varchar(255) DEFAULT NULL COMMENT '密码',
  `status` int(11) DEFAULT NULL COMMENT '1-可用 其他待定',
  `phone_number` varchar(32) DEFAULT NULL COMMENT '手机号',
  `phone_area` varchar(32) DEFAULT NULL COMMENT '手机号地区',
  `channel` int(11) DEFAULT NULL COMMENT '注册渠道: 1-手机号 2-google 3-discord 4-游客 5-微信 6-苹果 7-脸书 8-推特',
  `apple_union_id` varchar(255) DEFAULT NULL COMMENT '苹果unionid',
  `apple_nickname` varchar(64) DEFAULT NULL,
  `wx_union_id` varchar(255) DEFAULT NULL,
  `wx_nickname` varchar(64) DEFAULT NULL,
  `google_email` varchar(255) DEFAULT NULL,
  `google_nickname` varchar(64) DEFAULT NULL,
  `invitation_code` char(6) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `phone_idx` (`phone_number`,`phone_area`) USING BTREE,
  UNIQUE KEY `apple_idx` (`apple_union_id`) USING BTREE,
  UNIQUE KEY `wechat_idx` (`wx_union_id`) USING BTREE,
  UNIQUE KEY `google_idx` (`google_email`) USING BTREE,
  UNIQUE KEY `external_idx` (`external_user_id`) USING BTREE,
  KEY `nick_name` (`nick_name`)
) ENGINE=InnoDB AUTO_INCREMENT=731 DEFAULT CHARSET=utf8mb4 COMMENT='用户表'
```

## 表: account_daily_checkin_config

```sql
CREATE TABLE `account_daily_checkin_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `reward` decimal(5,2) NOT NULL DEFAULT '0.50' COMMENT '奖励小时',
  `remark` varchar(255) DEFAULT NULL,
  `platform` tinyint(4) NOT NULL DEFAULT '1' COMMENT '平台：1手机，2pc，3xbox',
  `consecutive_days` int(11) NOT NULL DEFAULT '0' COMMENT '累计签到天数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='用户每日签到奖励配置表'
```

## 表: account_daily_checkin_record

```sql
CREATE TABLE `account_daily_checkin_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) unsigned NOT NULL,
  `checkin_date` date NOT NULL COMMENT '签到日期',
  `consecutive_days` int(11) NOT NULL DEFAULT '0' COMMENT '累计签到天数',
  `reward` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '赠送时间，小时',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `platform` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1mobile,2pc,3xbox',
  `bind_phone` varchar(255) DEFAULT NULL COMMENT '绑定的手机号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_account_date` (`account_id`,`checkin_date`,`platform`) USING BTREE,
  UNIQUE KEY `unique_account_con` (`account_id`,`consecutive_days`,`platform`) USING BTREE,
  KEY `bind_phone_con` (`bind_phone`,`consecutive_days`,`platform`) USING BTREE,
  KEY `bind_phone_date` (`bind_phone`,`checkin_date`,`platform`)
) ENGINE=InnoDB AUTO_INCREMENT=232 DEFAULT CHARSET=utf8mb4 COMMENT='用户每日签到记录表'
```

## 表: account_info

```sql
CREATE TABLE `account_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `device_uuid` varchar(255) DEFAULT NULL,
  `system_channel` int(11) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `delsign` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `account_id_idx` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=492 DEFAULT CHARSET=utf8mb4
```

## 表: account_login_device

```sql
CREATE TABLE `account_login_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `token` varchar(64) DEFAULT NULL,
  `device_name` varchar(64) DEFAULT NULL,
  `system` varchar(64) DEFAULT NULL,
  `system_channel` int(11) DEFAULT NULL COMMENT '1 苹果 2 安卓 3 pc 4 鸿蒙\n',
  `model` varchar(64) DEFAULT NULL,
  `status` int(11) DEFAULT NULL COMMENT '0 登录 1 踢出',
  `device_uuid` varchar(255) DEFAULT NULL COMMENT '设备号,只允许小写字母和数字',
  `last_connection_time` bigint(20) DEFAULT NULL COMMENT '最后一次建立连接时间',
  `connection_duration` int(11) DEFAULT '0' COMMENT '连接总时长',
  `vpn_status` int(11) DEFAULT NULL COMMENT '0-断开 1-连接中',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `vpn_u` varchar(64) DEFAULT NULL,
  `device_token` varchar(256) DEFAULT NULL,
  `device_model` int(11) DEFAULT NULL COMMENT '0 小米 1 华为 2 苹果',
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `account_idx` (`account_id`,`device_uuid`,`vpn_u`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3639 DEFAULT CHARSET=utf8mb4
```

## 表: account_login_device_record

```sql
CREATE TABLE `account_login_device_record` (
  `id` bigint(20) NOT NULL,
  `login_device_id` bigint(20) DEFAULT NULL,
  `type` int(11) NOT NULL COMMENT '0 登录 1 踢出 2 新增',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
```

## 表: account_login_record

```sql
CREATE TABLE `account_login_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `token` varchar(255) NOT NULL,
  `client_ip` varchar(32) DEFAULT NULL COMMENT '登录ip',
  `oaid` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `token_uni` (`token`) USING BTREE,
  KEY `accountid_idx` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10318 DEFAULT CHARSET=utf8mb4 COMMENT='登录记录表'
```

## 表: account_membership_vpn_rule

```sql
CREATE TABLE `account_membership_vpn_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `membership_id` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  `system_channel` int(11) DEFAULT NULL COMMENT '1ios 2安卓 3pc 4鸿蒙',
  `connection_type` int(11) DEFAULT NULL COMMENT '1游戏 2影音 3学习',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COMMENT='会员vpn连接规则'
```

## 表: account_privilege_config

```sql
CREATE TABLE `account_privilege_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL COMMENT 'accountid',
  `privilege_id` int(11) DEFAULT NULL COMMENT '1-老板测试线路用\n2-给这个用户的这条线路强制分配这个机器',
  `server_id` int(11) DEFAULT NULL COMMENT 'pid=2时强制分配这个机器',
  `region_id` int(11) DEFAULT NULL COMMENT 'pid=2时强制分配这个机器',
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_id` (`account_id`,`privilege_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4
```

## 表: account_register_add_time_record

```sql
CREATE TABLE `account_register_add_time_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_id_idx` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=133 DEFAULT CHARSET=utf8mb4
```

## 表: account_reset_password_record

```sql
CREATE TABLE `account_reset_password_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `old_passward` varchar(255) DEFAULT NULL,
  `new_passward` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4
```

## 表: account_sign_out_data_record

```sql
CREATE TABLE `account_sign_out_data_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `nick_name` varchar(255) DEFAULT NULL COMMENT '昵称',
  `user_name` varchar(255) DEFAULT NULL COMMENT '用户名',
  `external_user_id` bigint(20) DEFAULT NULL COMMENT '外显id',
  `password` varchar(255) DEFAULT NULL COMMENT '密码',
  `status` int(11) DEFAULT NULL COMMENT '1-可用 其他待定',
  `phone_number` varchar(32) DEFAULT NULL COMMENT '手机号',
  `phone_area` varchar(32) DEFAULT NULL COMMENT '手机号地区',
  `channel` int(11) DEFAULT NULL COMMENT '注册渠道: 1-手机号 2-google 3-discord 4-游客 5-微信 6-苹果 7-脸书 8-推特',
  `apple_union_id` varchar(255) DEFAULT NULL COMMENT '苹果unionid',
  `apple_nickname` varchar(64) DEFAULT NULL,
  `wx_union_id` varchar(255) DEFAULT NULL,
  `wx_nickname` varchar(64) DEFAULT NULL,
  `google_email` varchar(255) DEFAULT NULL,
  `google_nickname` varchar(64) DEFAULT NULL,
  `invitation_code` char(6) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `delsign` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `nick_name` (`nick_name`),
  KEY `phone_idx` (`phone_number`,`phone_area`) USING BTREE,
  KEY `apple_idx` (`apple_union_id`) USING BTREE,
  KEY `wechat_idx` (`wx_union_id`) USING BTREE,
  KEY `google_idx` (`google_email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=540 DEFAULT CHARSET=utf8mb4 COMMENT='用户表'
```

## 表: account_sign_out_record

```sql
CREATE TABLE `account_sign_out_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `device_name` varchar(255) DEFAULT NULL,
  `system` varchar(255) DEFAULT NULL,
  `model` varchar(255) DEFAULT NULL,
  `device_uuid` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=277 DEFAULT CHARSET=utf8mb4
```

## 表: account_subscription_payment_platform

```sql
CREATE TABLE `account_subscription_payment_platform` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `subscription_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 订阅 1 未订阅',
  `subscription_platform` int(11) NOT NULL COMMENT '支付平台 1 Apple 2 PayPal 3  AliPay 4 WeChatPay 5 GooglePay',
  `subscription_expires_time` datetime DEFAULT NULL,
  `out_contract_code` varchar(255) DEFAULT NULL COMMENT '外部订单id',
  `contract_id` varchar(255) DEFAULT NULL COMMENT '微信签订合同id',
  `product_id` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `account_idx` (`account_id`,`subscription_platform`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4
```

## 表: account_subscription_record

```sql
CREATE TABLE `account_subscription_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `membership_id` int(11) DEFAULT NULL,
  `record_id` bigint(20) DEFAULT NULL,
  `transaction_id` bigint(20) DEFAULT NULL,
  `subscription_type` int(11) DEFAULT NULL COMMENT '0 免费 1 订阅 2 续订 3 内购 4 订阅升级 5 订阅降级 6 退款',
  `add_seconds` int(11) DEFAULT NULL,
  `end_time` datetime NOT NULL COMMENT '截止时间',
  `channel` int(11) DEFAULT NULL COMMENT '0: 苹果 1:paypal 2:google 3 支付宝 4:签到 5兑换码 6:邀请码 7 微信',
  `create_date` date DEFAULT NULL,
  `create_time` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=723 DEFAULT CHARSET=utf8mb4
```

## 表: account_subscription_status

```sql
CREATE TABLE `account_subscription_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `membership_id` int(11) NOT NULL COMMENT '1 手游会员 2 黄金会员 3 黑金会员 ',
  `end_time` datetime NOT NULL,
  `status` int(11) NOT NULL COMMENT '会员生效状态 0：生效 1:失效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `account_idx` (`account_id`,`membership_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=897 DEFAULT CHARSET=utf8mb4
```

## 表: account_subscription_stripe_status

```sql
CREATE TABLE `account_subscription_stripe_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `stripe_customer_id` varchar(128) DEFAULT NULL COMMENT 'stripe平台关联customer_id',
  `stripe_subscription_id` varchar(128) DEFAULT NULL COMMENT 'stripe平台订阅id',
  `stripe_subscription_status` varchar(64) DEFAULT 'incomplete' COMMENT 'status 字段可能的取值有：incomplete（未完成）、incomplete_expired（未完成过期）、trialing（试用中）、active（活跃）、past_due（逾期）、canceled（已取消）、unpaid（未支付）和 paused（暂停）。',
  `clientSecret` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `account_idx` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4
```

## 表: ad_info

```sql
CREATE TABLE `ad_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL COMMENT '广告名称',
  `system_channel` int(11) DEFAULT NULL COMMENT '1 苹果 2 安卓 3 pc 4 鸿蒙\n',
  `loading_image` varchar(200) NOT NULL COMMENT '开屏图片',
  `tend_url` text NOT NULL COMMENT '开屏点击后的跳转地址',
  `ad_time` int(11) NOT NULL DEFAULT '5' COMMENT '广告显示时间 单位（s）',
  `type` int(11) DEFAULT NULL COMMENT '0: 开屏 1: 个人页banner 2: pc广告',
  `publish_time` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `delsign` tinyint(1) DEFAULT '0' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4
```

## 表: admin_ip_allocation

```sql
CREATE TABLE `admin_ip_allocation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ecs_config_relation_id` int(11) DEFAULT NULL COMMENT 'ecs 配置对 id',
  `ip_last` int(11) NOT NULL COMMENT 'IP 地址的最后一段',
  `is_using` tinyint(4) DEFAULT '0' COMMENT '0 表示未占用，1 表示占用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_ip_last` (`ip_last`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=255 DEFAULT CHARSET=utf8mb4
```

## 表: admin_redemption_generate_record

```sql
CREATE TABLE `admin_redemption_generate_record` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `admin_user_id` int(11) NOT NULL COMMENT '管理员id',
  `action` varchar(255) DEFAULT NULL COMMENT '操作',
  `redemption_group_id` int(11) NOT NULL COMMENT '兑换码组id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COMMENT='管理员生成兑换码（组）表'
```

## 表: admin_route

```sql
CREATE TABLE `admin_route` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `path` varchar(255) NOT NULL COMMENT '路由',
  `name` varchar(255) DEFAULT NULL COMMENT '排序',
  `sort` int(11) DEFAULT NULL,
  `group_id` int(11) DEFAULT '1' COMMENT '所属的组id',
  `mark` varchar(255) DEFAULT NULL COMMENT '描述',
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  `level` int(11) DEFAULT '1' COMMENT '路由级别',
  `front_path` varchar(255) NOT NULL COMMENT '前端路由',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4
```

## 表: admin_user

```sql
CREATE TABLE `admin_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nickname` varchar(255) DEFAULT NULL COMMENT '用户昵称',
  `name` varchar(255) NOT NULL COMMENT '管理员名称',
  `password` varchar(11) NOT NULL COMMENT '管理员密码',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4
```

## 表: admin_user_login_log

```sql
CREATE TABLE `admin_user_login_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_user_id` int(11) NOT NULL,
  `device` varchar(255) DEFAULT NULL COMMENT '设备信息',
  `ip` varchar(255) DEFAULT NULL COMMENT '登录ip',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expire_time` timestamp NULL DEFAULT NULL,
  `expire_day` int(11) DEFAULT NULL COMMENT '登录令牌超时时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1446 DEFAULT CHARSET=utf8mb4
```

## 表: admin_user_operate_log

```sql
CREATE TABLE `admin_user_operate_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `path` varchar(255) DEFAULT NULL COMMENT '接口地址',
  `admin_user_id` int(11) NOT NULL,
  `req_json` text,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `device` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29245 DEFAULT CHARSET=utf8mb4
```

## 表: admin_user_operate_record

```sql
CREATE TABLE `admin_user_operate_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `admin_user_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `account_user_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `opt` tinyint(4) DEFAULT NULL COMMENT '1.踢人 2.封号',
  `remark` text COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `delsign` tinyint(4) DEFAULT NULL COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='用户处理记录表'
```

## 表: admin_user_route

```sql
CREATE TABLE `admin_user_route` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_user_id` int(11) DEFAULT NULL,
  `route_id` int(11) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `grantor_id` int(11) DEFAULT NULL COMMENT '授权人id',
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `user_route_id` (`admin_user_id`,`route_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=360 DEFAULT CHARSET=utf8mb4
```

## 表: announcement

```sql
CREATE TABLE `announcement` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `title` varchar(100) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `publish_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '发布时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0. 存在 1.删除',
  `template_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '0表示没有模版无法编辑',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_publish_time` (`publish_time`) USING BTREE,
  KEY `template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=578 DEFAULT CHARSET=utf8mb4 COMMENT='公告表'
```

## 表: announcement_template

```sql
CREATE TABLE `announcement_template` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_list` text NOT NULL COMMENT '用户id以逗号分割，0表示全部用户',
  `title` varchar(100) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `publish_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '发布时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COMMENT='公告表'
```

## 表: app_info

```sql
CREATE TABLE `app_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `platform` varchar(20) NOT NULL COMMENT 'ios, android, huawei',
  `app_version` varchar(20) NOT NULL COMMENT '客户端版本号',
  `build_version` varchar(20) NOT NULL COMMENT '客户端build版本号',
  `version` varchar(20) NOT NULL COMMENT '迭代版本号',
  `pc_game_version` double(20,4) DEFAULT NULL COMMENT 'PC游戏库版本',
  `download_url` text COMMENT 'android apk下载地址,pc游戏库下载地址',
  `data_download_url` text COMMENT '数据下载链接',
  `status` tinyint(4) DEFAULT NULL COMMENT '0:正常，1:审核',
  `web_url` json DEFAULT NULL COMMENT '静态网页(隐私政策、服务协议、自动续费协议、注销账号协议)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0. 存在 1.删除',
  `sub_platform` varchar(20) DEFAULT NULL COMMENT '安卓具体渠道',
  `pc_update_remark` text COMMENT '版本发布说明',
  `pc_update_time` varchar(30) DEFAULT '' COMMENT '版本发布时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4
```

## 表: app_info_copy

```sql
CREATE TABLE `app_info_copy` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `platform` varchar(20) NOT NULL COMMENT 'ios, android, huawei',
  `app_version` varchar(20) NOT NULL COMMENT '客户端版本号',
  `build_version` varchar(20) NOT NULL COMMENT '客户端build版本号',
  `version` varchar(20) NOT NULL COMMENT '迭代版本号',
  `pc_game_version` double(20,4) DEFAULT NULL COMMENT 'PC游戏库版本',
  `download_url` text COMMENT 'android apk下载地址,pc游戏库下载地址',
  `data_download_url` text COMMENT '数据下载链接',
  `status` tinyint(4) DEFAULT NULL COMMENT '0:正常，1:审核',
  `web_url` json DEFAULT NULL COMMENT '静态网页(隐私政策、服务协议、自动续费协议、注销账号协议)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0. 存在 1.删除',
  `sub_platform` varchar(20) DEFAULT NULL COMMENT '安卓具体渠道',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4
```

## 表: channel_biz

```sql
CREATE TABLE `channel_biz` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `channel_type` tinyint(4) NOT NULL COMMENT '频道类型:0公告,1系统消息,2活动通知等',
  `channel_table` varchar(50) NOT NULL COMMENT '模块唯一标识符',
  `desc` varchar(100) NOT NULL COMMENT '描述',
  `delsign` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_code` (`channel_table`) USING BTREE,
  KEY `idx_channel_type` (`channel_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='功能模块映射表'
```

## 表: extent_data

```sql
CREATE TABLE `extent_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `oaid` varchar(255) DEFAULT NULL,
  `imei` varchar(255) DEFAULT NULL,
  `callback` varchar(255) DEFAULT NULL,
  `task_id` varchar(255) DEFAULT NULL,
  `task_name` varchar(255) DEFAULT NULL,
  `action_type` varchar(255) DEFAULT NULL COMMENT 'IMP：对应精准曝光监测链接\nCLICK：对应点击上报监测链接\nDOWNLOAD：对应下载上报监测链接\nINSTALL：对应安装上报监测链接\nDEEPLINKCLICK：对应打开跳转deeplink上报监测链接\n',
  `id_type` varchar(255) NOT NULL COMMENT '0 IMEI',
  `delsign` int(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='华为推广返回oaid数据'
```

## 表: extent_device_open_record

```sql
CREATE TABLE `extent_device_open_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `oaid` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COMMENT='华为推广打开设备数据'
```

## 表: extent_event_record

```sql
CREATE TABLE `extent_event_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `oaid` varchar(255) DEFAULT NULL,
  `action_type` int(11) NOT NULL COMMENT '		1：应用激活\n		2：启动应用\n		3：次日留存\n		4：付费\n		5：提交表单\n		6：授信\n		7：注册\n		9：线索收集页面访问\n		10：老客激活\n		11：完件\n		12：支用\n		13：还款\n		14：申请\n		18：下单\n		21：预约\n		101：关键行为1\n		102：关键行为2\n		1001：平台首日ROI\n',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='华为推广事件记录表'
```

## 表: extent_login_oaid

```sql
CREATE TABLE `extent_login_oaid` (
  `id` bigint(20) NOT NULL,
  `account_id` bigint(20) DEFAULT NULL,
  `oaid` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录oaid记录'
```

## 表: feedback

```sql
CREATE TABLE `feedback` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '反馈类型：0游戏反馈，1问题反馈，2加速异常，3缺少游戏或区服，4会员充值，5优化建议，6加速效果不理想, 7网络环境改善, 8加速达不到预期, 9没有想要的游戏, 10同类产品更好, 11其他问题\r\n',
  `content` text NOT NULL COMMENT '反馈内容',
  `email` varchar(255) DEFAULT NULL COMMENT '联系邮箱',
  `account_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `system_channel` tinyint(4) DEFAULT NULL COMMENT '1 苹果 2 安卓 3 PC',
  `announcement_id` bigint(20) DEFAULT NULL COMMENT '反馈回复表id',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0待处理,1处理中,2已完成,3处理失败,4已超时',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '状态更新时间',
  `delsign` tinyint(1) DEFAULT '0' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`account_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_created_at` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=167 DEFAULT CHARSET=utf8mb4 COMMENT='用户反馈表'
```

## 表: feedback_image

```sql
CREATE TABLE `feedback_image` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `feedback_id` bigint(20) unsigned NOT NULL COMMENT '反馈ID',
  `url` varchar(255) NOT NULL COMMENT '图片URL',
  `size` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '图片大小(字节)',
  `format` varchar(10) NOT NULL COMMENT '图片格式',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_feedback_id` (`feedback_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='反馈图片表'
```

## 表: feedback_record

```sql
CREATE TABLE `feedback_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `feedback_id` bigint(20) unsigned NOT NULL COMMENT '反馈ID',
  `admin_user_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `status_before` varchar(255) DEFAULT NULL COMMENT '操作前的值',
  `status_after` varchar(255) DEFAULT NULL COMMENT '操作后的值',
  `remark` text COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_feedback_id` (`feedback_id`) USING BTREE,
  KEY `idx_admin_id` (`admin_user_id`) USING BTREE,
  KEY `idx_created_at` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='反馈处理记录表'
```

## 表: game

```sql
CREATE TABLE `game` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '游戏名称',
  `subtitle` varchar(100) DEFAULT NULL COMMENT '子标题',
  `apple_app_id` varchar(64) DEFAULT NULL,
  `android_bundle_id` varchar(100) DEFAULT NULL COMMENT 'Android BundleId',
  `pc_exe_file` varchar(100) DEFAULT NULL COMMENT 'PC游戏执行文件名称',
  `pc_process_exe` varchar(255) DEFAULT NULL COMMENT '进程名',
  `pc_display_name` varchar(100) DEFAULT NULL COMMENT 'pc游戏显示名称',
  `pc_display_name_loc` json DEFAULT NULL COMMENT 'pc游戏显示名称, json',
  `image_url` varchar(255) NOT NULL COMMENT '游戏图片',
  `game_url` text COMMENT '游戏链接地址，官网，下载地址等',
  `startup_url` json DEFAULT NULL COMMENT '{"steam":"","exe":"","epic":"","blizzard":"","ea":"","wegame":"","ubisoft":""}',
  `is_foreign` tinyint(4) NOT NULL COMMENT '0.国内ip 1.国外ip',
  `is_free` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:不免费加速, 1:免费加速',
  `is_show` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:不外显, 1:外显',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，降序',
  `default_region` int(11) DEFAULT '-1' COMMENT '默认区服，自动区服',
  `is_default` int(11) NOT NULL DEFAULT '0' COMMENT '0. 非默认，1. 默认',
  `delay` int(11) NOT NULL DEFAULT '0' COMMENT '网络延迟',
  `mark` varchar(500) DEFAULT NULL COMMENT '游戏简介',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `game_id` bigint(20) DEFAULT '0' COMMENT 'game表唯一id，0表示未关联',
  `cate` bigint(20) NOT NULL DEFAULT '0' COMMENT '0:线路，1:影音，2:移动游戏，3:pc游戏',
  `ios_delsign` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0. 存在 1.删除',
  `delsign` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_created_at` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=********** DEFAULT CHARSET=utf8mb4 COMMENT='游戏信息表'
```

## 表: game_account_relation

```sql
CREATE TABLE `game_account_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) unsigned NOT NULL COMMENT '用户id',
  `game_id` bigint(20) unsigned NOT NULL COMMENT '游戏id',
  `region_id` int(11) DEFAULT NULL COMMENT '游戏服务器区域id',
  `is_connecting` tinyint(4) DEFAULT NULL COMMENT '是否正在连接 0.未连接 1.正在连接',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_id`,`account_id`) USING BTREE,
  KEY `idx_tag_id` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13274 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签关联表'
```

## 表: game_bak

```sql
CREATE TABLE `game_bak` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '游戏名称',
  `apple_app_id` varchar(64) DEFAULT NULL,
  `android_bundle_id` varchar(100) DEFAULT NULL COMMENT 'Android BundleId',
  `image_url` varchar(255) NOT NULL COMMENT '游戏图片',
  `is_foreign` tinyint(4) NOT NULL COMMENT '0.国内ip 1.国外ip',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，降序',
  `is_default` int(11) NOT NULL DEFAULT '0' COMMENT '0. 非默认，1. 默认',
  `delay` int(11) NOT NULL DEFAULT '0' COMMENT '网络延迟',
  `mark` varchar(500) DEFAULT NULL COMMENT '游戏简介',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `delsign` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_created_at` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=********** DEFAULT CHARSET=utf8mb4 COMMENT='游戏信息表'
```

## 表: game_device_relation

```sql
CREATE TABLE `game_device_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) unsigned NOT NULL COMMENT '用户id',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备id',
  `game_id` bigint(20) unsigned NOT NULL COMMENT '游戏id',
  `region_id` int(11) DEFAULT NULL COMMENT '游戏服务器区域id',
  `is_connected` tinyint(4) DEFAULT '0' COMMENT '是否连接 0.未连接 1.已连接',
  `create_time` timestamp NULL DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `last_connect_time` timestamp NULL DEFAULT NULL COMMENT '上次连接时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`device_id`,`account_id`,`game_id`) USING BTREE,
  KEY `idx_tag_id` (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13161 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签关联表'
```

## 表: game_dns_log_analyze

```sql
CREATE TABLE `game_dns_log_analyze` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `dns_id` int(11) DEFAULT NULL,
  `since` datetime DEFAULT NULL,
  `until` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `domain` (`domain`,`ip`),
  KEY `ip` (`ip`)
) ENGINE=InnoDB AUTO_INCREMENT=2936 DEFAULT CHARSET=utf8mb4
```

## 表: game_dns_log_cursor

```sql
CREATE TABLE `game_dns_log_cursor` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dns_id` int(11) DEFAULT NULL,
  `last_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4
```

## 表: game_domain_blackwhitelist

```sql
CREATE TABLE `game_domain_blackwhitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '游戏ID',
  `game_region_id` int(11) NOT NULL,
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否在黑名单',
  `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `is_foreign` tinyint(4) DEFAULT NULL COMMENT '0-国内 1-国外',
  `protocol` varchar(32) DEFAULT NULL COMMENT '协议',
  `common_blacklist_id` int(11) DEFAULT NULL COMMENT '非空表示通用黑名单',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`game_id`,`game_region_id`,`domain_name`,`protocol`) USING BTREE,
  KEY `domain_name` (`domain_name`)
) ENGINE=InnoDB AUTO_INCREMENT=57285 DEFAULT CHARSET=utf8mb4 COMMENT='域名黑名单表'
```

## 表: game_domain_blackwhitelist_record

```sql
CREATE TABLE `game_domain_blackwhitelist_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `channel` int(11) DEFAULT NULL COMMENT '1-人工添加\n2-UU加速器\n3-wireshark解析\n4-虚拟机抓取\n5-通用黑名单\n6-不黑不白\n7-vpn检测上报\n8-客户端上报\n9-v2ray抓取\n',
  `channel_status` int(11) DEFAULT NULL COMMENT '-1-不黑不白需要删除\n0-白名单\n1-黑名单',
  `is_foreign` int(11) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`domain`,`channel`,`protocol`,`is_foreign`) USING BTREE,
  KEY `domain` (`domain`)
) ENGINE=InnoDB AUTO_INCREMENT=48960 DEFAULT CHARSET=utf8mb4 COMMENT='域名渠道记录表'
```

## 表: game_domain_blackwhitelist_v2

```sql
CREATE TABLE `game_domain_blackwhitelist_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `domain_name` varchar(255) DEFAULT NULL,
  `is_black` int(11) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `is_foreigin` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  `channel` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`domain_name`,`protocol`,`is_foreigin`),
  KEY `domain_name` (`domain_name`)
) ENGINE=InnoDB AUTO_INCREMENT=761533 DEFAULT CHARSET=utf8mb4 COMMENT='域名最终状态表'
```

## 表: game_domain_channel_rank

```sql
CREATE TABLE `game_domain_channel_rank` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `channel` int(11) DEFAULT NULL COMMENT 'CHANNEL_MANUAL = 1\nCHANNEL_UU = 2\nCHANNEL_WIRESHARK = 3\nCHANNEL_VM = 4\nCHANNEL_COMMON_BLACKLIST = 5\nCHANNEL_IGNORE = 6\nCHANNEL_AUTO_DETECT = 7\nCHANNEL_PC_REPORT = 8\nCHANNEL_V2RAY = 9',
  `rank_order` int(11) DEFAULT NULL COMMENT '排名顺序，数值越小排名越靠前',
  `delsign` int(11) NOT NULL DEFAULT '0' COMMENT '0=正常，1=删除',
  PRIMARY KEY (`id`),
  KEY `idx_channel_rank` (`channel`,`delsign`,`rank_order`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4
```

## 表: game_domain_dns

```sql
CREATE TABLE `game_domain_dns` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) unsigned NOT NULL,
  `domain` varchar(100) NOT NULL COMMENT '域名',
  `delsign` tinyint(1) DEFAULT '0' COMMENT '删除标志位0有效1删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_game_id` (`game_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2648 DEFAULT CHARSET=utf8mb4 COMMENT='游戏测速域名表'
```

## 表: game_ip_blackwhitelist

```sql
CREATE TABLE `game_ip_blackwhitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL COMMENT '游戏ID',
  `game_region_id` int(11) DEFAULT NULL,
  `ip` varchar(255) NOT NULL COMMENT 'IP地址',
  `ip_mask` varchar(64) DEFAULT NULL COMMENT '带掩码的',
  `resolve_domain` int(11) DEFAULT NULL COMMENT '域名解析出来的',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '黑名单？',
  `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `is_foreign` tinyint(4) DEFAULT NULL COMMENT '0-国内 1-国外',
  `protocol` varchar(32) DEFAULT NULL COMMENT '协议',
  `common_blacklist_id` int(11) DEFAULT NULL COMMENT '非空表示通用黑名单',
  `is_auto_detect` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否自动解析的 0-不是自动解析的 1-自动解出来的黑 2-自动解出来的白',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_idx` (`game_id`,`game_region_id`,`ip`,`protocol`) USING BTREE,
  KEY `resolve_domain` (`resolve_domain`) USING BTREE,
  KEY `ip_mask` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=6991936 DEFAULT CHARSET=utf8mb4 COMMENT='游戏IP黑白名单表'
```

## 表: game_ip_blackwhitelist_v2

```sql
CREATE TABLE `game_ip_blackwhitelist_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `ip_mask` varchar(255) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `is_black` int(11) DEFAULT NULL,
  `is_foreigin` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  `channel` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`ip_mask`,`protocol`,`is_foreigin`),
  KEY `ip_mask` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=3765527 DEFAULT CHARSET=utf8mb4 COMMENT='Ip最终状态表'
```

## 表: game_ip_mask_blackwhitelist_record

```sql
CREATE TABLE `game_ip_mask_blackwhitelist_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ip_mask` varchar(255) NOT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `channel` int(11) DEFAULT NULL COMMENT '1-人工添加\n2-UU加速器\n3-wireshark解析\n4-虚拟机抓取\n5-通用黑名单\n6-不黑不白\n7-vpn检测上报\n8-客户端上报\n9-v2ray抓取\n',
  `channel_status` int(11) DEFAULT NULL COMMENT '-1-不黑不白需要删除\n0-白名单\n1-黑名单',
  `resolve_domain` bigint(20) DEFAULT NULL,
  `is_foreign` int(11) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ip_mask` (`game_id`,`game_region_id`,`ip_mask`,`channel`,`is_foreign`,`protocol`) USING BTREE,
  KEY `ip_mask_2` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=3923661 DEFAULT CHARSET=utf8mb4 COMMENT='Ip渠道记录表'
```

## 表: game_location

```sql
CREATE TABLE `game_location` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `location` varchar(5) NOT NULL,
  `name` varchar(50) NOT NULL,
  `delsign` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `location` (`location`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COMMENT='爬取游戏地区关联表'
```

## 表: game_market

```sql
CREATE TABLE `game_market` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4
```

## 表: game_pc_router

```sql
CREATE TABLE `game_pc_router` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `ip_mask` varchar(64) DEFAULT NULL,
  `channel` int(11) DEFAULT NULL COMMENT '1-UU',
  `type` int(11) DEFAULT NULL COMMENT '1-本机路由 2-vpn加速器路由',
  `metric` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `protocol` varchar(32) DEFAULT NULL,
  `country_code` varchar(32) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip_mask` (`user_id`,`ip_mask`) USING BTREE,
  UNIQUE KEY `ip_mask_2` (`game_id`,`game_region_id`,`ip_mask`) USING BTREE,
  KEY `ip_mask_3` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=75296215 DEFAULT CHARSET=utf8mb4
```

## 表: game_pc_router_domain

```sql
CREATE TABLE `game_pc_router_domain` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `domain` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `channel` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`domain`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=353 DEFAULT CHARSET=utf8mb4
```

## 表: game_platform

```sql
CREATE TABLE `game_platform` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '游戏名称',
  `image_url` varchar(255) NOT NULL COMMENT '游戏图片',
  `is_foreign` tinyint(4) NOT NULL COMMENT '0.国内ip 1.国外ip',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，降序',
  `is_default` int(11) NOT NULL DEFAULT '0' COMMENT '0. 非默认，1. 默认',
  `delay` int(11) NOT NULL DEFAULT '0' COMMENT '网络延迟',
  `mark` varchar(500) DEFAULT NULL COMMENT '游戏简介',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `bundle_id` varchar(100) NOT NULL COMMENT '平台唯一性包名',
  `system_channel` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1ios 2android 3pc 4鸿蒙',
  `game_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'game表主键，默认0表示未关联',
  `delsign` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0. 存在 1.删除',
  `apple_id` bigint(20) DEFAULT NULL COMMENT 'Appstore平台的id，安卓平台没有该字段',
  `developer` varchar(200) DEFAULT NULL,
  `developerUrl` varchar(200) DEFAULT NULL,
  `developerId` varchar(200) DEFAULT NULL,
  `genre` varchar(200) DEFAULT NULL,
  `genreId` varchar(200) DEFAULT NULL,
  `market_app_id` varchar(100) DEFAULT NULL COMMENT '应用市场App_id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `bundle_system_id` (`bundle_id`,`system_channel`) USING BTREE,
  KEY `idx_created_at` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=92250 DEFAULT CHARSET=utf8mb4 COMMENT='爬取游戏（区分平台）信息表'
```

## 表: game_platform_location_relation

```sql
CREATE TABLE `game_platform_location_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_platform_id` bigint(20) unsigned NOT NULL,
  `game_location_id` bigint(20) unsigned NOT NULL,
  `delsign` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_platform_id`,`game_location_id`) USING BTREE,
  KEY `idx_tag_id` (`game_location_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=90227 DEFAULT CHARSET=utf8mb4 COMMENT='爬取游戏地区关联表'
```

## 表: game_platform_market_relation

```sql
CREATE TABLE `game_platform_market_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_platform_id` bigint(20) unsigned NOT NULL,
  `game_market_id` bigint(20) unsigned NOT NULL,
  `delsign` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_platform_id`,`game_market_id`) USING BTREE,
  KEY `idx_tag_id` (`game_market_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2620 DEFAULT CHARSET=utf8mb4 COMMENT='爬取游戏地区关联表'
```

## 表: game_platform_tag_relation

```sql
CREATE TABLE `game_platform_tag_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_platform_id` bigint(20) unsigned NOT NULL,
  `tag_id` bigint(20) unsigned NOT NULL,
  `delsign` tinyint(1) DEFAULT '0' COMMENT '删除标志位0有效1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_platform_id`,`tag_id`) USING BTREE,
  KEY `idx_tag_id` (`tag_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=90227 DEFAULT CHARSET=utf8mb4 COMMENT='爬取游戏标签（区分平台）关联表'
```

## 表: game_region

```sql
CREATE TABLE `game_region` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `region` varchar(50) NOT NULL COMMENT '游戏服务器区域',
  `is_foreign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0.国内ip 1.国外ip',
  `sort` int(11) NOT NULL DEFAULT '99999' COMMENT '排序，正序',
  `remark` varchar(50) DEFAULT NULL,
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_name` (`region`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=90002 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签表'
```

## 表: game_region_liner_relation

```sql
CREATE TABLE `game_region_liner_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_region_id` bigint(20) unsigned NOT NULL COMMENT '游戏区服id',
  `liner_id` int(10) unsigned NOT NULL COMMENT 'liner表id',
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_region_id`,`liner_id`) USING BTREE,
  KEY `idx_tag_id` (`game_region_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=931 DEFAULT CHARSET=utf8mb4 COMMENT='游戏区服-线路关联表'
```

## 表: game_region_relation

```sql
CREATE TABLE `game_region_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) unsigned NOT NULL,
  `game_region_id` bigint(20) unsigned NOT NULL,
  `ios_delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0. 存在 1.删除',
  `delsign` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_id`,`game_region_id`) USING BTREE,
  KEY `idx_tag_id` (`game_region_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3869 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签关联表'
```

## 表: game_server_region_relation

```sql
CREATE TABLE `game_server_region_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_region_id` bigint(20) unsigned NOT NULL COMMENT '游戏区服',
  `server_region_id` bigint(20) unsigned NOT NULL COMMENT '服务器地区',
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_region_id`,`server_region_id`) USING BTREE,
  KEY `idx_tag_id` (`game_region_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=608 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签关联表'
```

## 表: game_stats

```sql
CREATE TABLE `game_stats` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) unsigned NOT NULL COMMENT '游戏ID',
  `game_region_id` int(11) NOT NULL,
  `online_account` int(10) NOT NULL DEFAULT '0' COMMENT '当前加速人数',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_id` (`game_id`,`game_region_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8242 DEFAULT CHARSET=utf8mb4 COMMENT='实时统计数据表'
```

## 表: game_tag

```sql
CREATE TABLE `game_tag` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `en_name` varchar(50) DEFAULT NULL,
  `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:手机游戏类别 ,1:苹果爬取类别,2:android爬取类别，3:pc游戏库类别，4:pc游戏启动类型',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，降序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_name` (`name`,`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=30009 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签表'
```

## 表: game_tag_relation

```sql
CREATE TABLE `game_tag_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) unsigned NOT NULL,
  `tag_id` bigint(20) unsigned NOT NULL,
  `delsign` tinyint(1) DEFAULT '0' COMMENT '删除标志位0有效1删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_id`,`tag_id`) USING BTREE,
  KEY `idx_tag_id` (`tag_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2836 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签关联表'
```

## 表: game_v2ray_config

```sql
CREATE TABLE `game_v2ray_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` bigint(20) NOT NULL DEFAULT '0',
  `game_name` varchar(255) DEFAULT NULL,
  `game_region_name` varchar(255) DEFAULT '国服',
  `client_uuid` varchar(255) DEFAULT NULL,
  `client_username` varchar(255) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `vless_ip` varchar(255) DEFAULT NULL,
  `vless_port` int(11) DEFAULT NULL,
  `test_vless_port` int(11) DEFAULT NULL,
  `game_is_foreign` int(11) DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 未操作 1: 开始 2: 结束 3: 记录 4: 测试 5: 同步 6: 已测',
  `user_id` bigint(20) DEFAULT NULL,
  `state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:default 1:ok 2: testing 3:pass 4:error  5:测试通过 6:已同步 7:tapd 8:无路由模式 9: 无权限 10:已验证 11:验证失败',
  `vpn_region` varchar(32) DEFAULT NULL COMMENT 'V2ray地区tag',
  `game_is_pc` int(11) NOT NULL DEFAULT '0' COMMENT '是否pc。0-非 1-是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_uni` (`game_id`,`game_region_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22347 DEFAULT CHARSET=utf8mb4
```

## 表: game_v2ray_config_state_log

```sql
CREATE TABLE `game_v2ray_config_state_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `old_status` int(11) DEFAULT NULL,
  `new_status` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `game_id` (`game_id`,`game_region_id`)
) ENGINE=InnoDB AUTO_INCREMENT=428 DEFAULT CHARSET=utf8mb4
```

## 表: game_v2ray_config_status_dic

```sql
CREATE TABLE `game_v2ray_config_status_dic` (
  `id` int(11) NOT NULL,
  `value` varchar(255) NOT NULL,
  `sorted` int(11) NOT NULL DEFAULT '0' COMMENT '排序小的在前',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
```

## 表: game_v2ray_domain_blacklist

```sql
CREATE TABLE `game_v2ray_domain_blacklist` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) DEFAULT NULL,
  `ip_mask` varchar(32) DEFAULT NULL,
  `is_foreign` tinyint(4) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniidx` (`domain`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=94 DEFAULT CHARSET=utf8mb4
```

## 表: game_v2ray_domain_blacklist_temp

```sql
CREATE TABLE `game_v2ray_domain_blacklist_temp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) DEFAULT NULL,
  `ip_mask` varchar(32) DEFAULT NULL,
  `is_foreign` tinyint(4) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniidx` (`domain`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=94 DEFAULT CHARSET=utf8mb4
```

## 表: game_v2ray_domain_whitelist

```sql
CREATE TABLE `game_v2ray_domain_whitelist` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `protocol` varchar(32) DEFAULT NULL,
  `domain` varchar(255) DEFAULT NULL,
  `game_region_id` bigint(20) DEFAULT NULL,
  `log_time` datetime DEFAULT NULL,
  `ping_delay_ms` double DEFAULT NULL,
  `status` int(11) DEFAULT '0',
  `traffic_bytes` bigint(20) DEFAULT '0',
  `traffic_bytes_temp` bigint(20) DEFAULT '0',
  `ip_mask` varchar(32) DEFAULT NULL,
  `vpn_region` varchar(64) DEFAULT NULL,
  `is_black` int(11) DEFAULT '0' COMMENT '0-正常 1-在黑',
  `is_manual` int(11) DEFAULT '0' COMMENT '0-自动 1-人工',
  `channel` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniidx` (`game_id`,`protocol`,`domain`,`game_region_id`,`channel`) USING BTREE,
  KEY `domain_idx` (`domain`) USING BTREE,
  KEY `ipmask_idx` (`ip_mask`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=803959 DEFAULT CHARSET=utf8mb4
```

## 表: game_v2ray_domain_whitelist_ignore

```sql
CREATE TABLE `game_v2ray_domain_whitelist_ignore` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='不黑不白过滤表'
```

## 表: game_v2ray_ip_mask

```sql
CREATE TABLE `game_v2ray_ip_mask` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ip` varchar(32) DEFAULT NULL,
  `region` varchar(32) DEFAULT NULL,
  `provider` varchar(64) DEFAULT NULL,
  `mask` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4
```

## 表: game_v2ray_log_opt

```sql
CREATE TABLE `game_v2ray_log_opt` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `opt_value` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` bigint(20) DEFAULT NULL,
  `ip` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2910 DEFAULT CHARSET=utf8mb4
```

## 表: game_v2ray_status

```sql
CREATE TABLE `game_v2ray_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_config_id` bigint(20) DEFAULT NULL,
  `checked` tinyint(4) DEFAULT '0' COMMENT '是否已爬 0 没有  1 已爬',
  `sync_flag` tinyint(4) DEFAULT NULL COMMENT '是否已完成线上同步',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=699 DEFAULT CHARSET=utf8mb4
```

## 表: game_v2ray_vpn_region_config

```sql
CREATE TABLE `game_v2ray_vpn_region_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `region_name` varchar(255) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4
```

## 表: livestream_config

```sql
CREATE TABLE `livestream_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `live_name` varchar(255) DEFAULT NULL,
  `code_group_id` int(11) NOT NULL,
  `append_num` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='直播配置表'
```

## 表: nongame_domain_blackwhitelist

```sql
CREATE TABLE `nongame_domain_blackwhitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '游戏ID',
  `game_region_id` int(11) DEFAULT NULL,
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否在黑名单',
  `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `is_foreign` tinyint(4) DEFAULT NULL COMMENT '0-国内 1-国外',
  `protocol` varchar(32) DEFAULT NULL COMMENT '协议',
  `common_blacklist_id` int(11) DEFAULT NULL COMMENT '非空表示通用黑名单',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_domain_name_unique` (`game_id`,`game_region_id`,`domain_name`,`protocol`) USING BTREE,
  KEY `domain_name` (`domain_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10876 DEFAULT CHARSET=utf8mb4 COMMENT='域名黑名单表'
```

## 表: nongame_domain_blackwhitelist_record

```sql
CREATE TABLE `nongame_domain_blackwhitelist_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `channel` int(11) DEFAULT NULL,
  `channel_status` int(11) DEFAULT NULL,
  `is_foreign` int(11) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`domain`,`channel`,`protocol`,`is_foreign`) USING BTREE,
  KEY `domain` (`domain`)
) ENGINE=InnoDB AUTO_INCREMENT=17485 DEFAULT CHARSET=utf8mb4
```

## 表: nongame_domain_blackwhitelist_v2

```sql
CREATE TABLE `nongame_domain_blackwhitelist_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `domain_name` varchar(255) DEFAULT NULL,
  `is_black` int(11) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `is_foreigin` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  `channel` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`domain_name`,`protocol`,`is_foreigin`),
  KEY `domain_name` (`domain_name`)
) ENGINE=InnoDB AUTO_INCREMENT=93131 DEFAULT CHARSET=utf8mb4
```

## 表: nongame_ip_blackwhitelist

```sql
CREATE TABLE `nongame_ip_blackwhitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` bigint(20) NOT NULL COMMENT '游戏ID',
  `game_region_id` int(11) DEFAULT NULL,
  `ip` varchar(255) NOT NULL COMMENT 'IP地址',
  `ip_mask` varchar(64) DEFAULT NULL COMMENT '带掩码的',
  `resolve_domain` int(11) DEFAULT NULL COMMENT '域名解析出来的',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标志：0保留，1删除',
  `is_black` tinyint(4) NOT NULL DEFAULT '0' COMMENT '黑名单？',
  `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `is_foreign` tinyint(4) DEFAULT NULL COMMENT '0-国内 1-国外',
  `protocol` varchar(32) DEFAULT NULL COMMENT '协议',
  `common_blacklist_id` int(11) DEFAULT NULL COMMENT '非空表示通用黑名单',
  `is_auto_detect` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否自动解析的',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_idx` (`game_id`,`game_region_id`,`ip`,`protocol`) USING BTREE,
  KEY `resolve_domain` (`resolve_domain`) USING BTREE,
  KEY `ip_mask` (`ip_mask`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5533993 DEFAULT CHARSET=utf8mb4 COMMENT='海外IP白名单表'
```

## 表: nongame_ip_blackwhitelist_v2

```sql
CREATE TABLE `nongame_ip_blackwhitelist_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `ip_mask` varchar(255) DEFAULT NULL,
  `resolve_domain` bigint(20) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `is_black` int(11) DEFAULT NULL,
  `is_foreigin` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  `channel` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`ip_mask`,`protocol`,`is_foreigin`),
  KEY `ip_mask` (`ip_mask`),
  KEY `resolve_domain` (`resolve_domain`)
) ENGINE=InnoDB AUTO_INCREMENT=98942 DEFAULT CHARSET=utf8mb4
```

## 表: nongame_ip_mask_blackwhitelist_record

```sql
CREATE TABLE `nongame_ip_mask_blackwhitelist_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ip_mask` varchar(255) NOT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `channel` int(11) DEFAULT NULL,
  `channel_status` int(11) DEFAULT NULL,
  `resolve_domain` bigint(20) DEFAULT NULL,
  `is_foreign` int(11) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ip_mask` (`game_id`,`game_region_id`,`ip_mask`,`channel`,`is_foreign`,`protocol`) USING BTREE,
  KEY `ip_mask_2` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=234004 DEFAULT CHARSET=utf8mb4
```

## 表: push

```sql
CREATE TABLE `push` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `push_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '发送类别：1-指定用户列表',
  `push_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '发送状态：0-未发送 1-发送中 2-发送成功 3-发送失败',
  `delay_send_time` datetime DEFAULT NULL COMMENT '计划发送时间(用于延迟发送)',
  `send_progress` int(11) NOT NULL DEFAULT '0' COMMENT '发送进度(苹果平台每次不超过100)',
  `send_total` int(11) NOT NULL DEFAULT '0' COMMENT '发送总数',
  `account_list` text COMMENT '用户账号列表(逗号分隔)',
  `title` varchar(255) NOT NULL COMMENT '推送标题',
  `description` text COMMENT '推送内容',
  `admin_user_id` bigint(20) NOT NULL COMMENT '操作管理员ID',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_push_status` (`push_status`),
  KEY `idx_send_time` (`delay_send_time`),
  KEY `idx_admin_user_id` (`admin_user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='推送任务表'
```

## 表: push_record

```sql
CREATE TABLE `push_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `push_channel` tinyint(4) NOT NULL COMMENT '推送渠道：-1未区分 0-Apns(苹果) 1-FCM 2-华为 3-小米',
  `system_channel` tinyint(4) NOT NULL COMMENT '设备平台：1-苹果 2-安卓 3-PC 4-鸿蒙',
  `push_id` bigint(20) NOT NULL COMMENT '关联的推送任务ID',
  `account_list` text COMMENT '实际发送的用户账号列表(逗号分隔)',
  `push_result` tinyint(4) NOT NULL COMMENT '推送结果：1-成功 2-失败',
  `fail_result` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_push_id` (`push_id`),
  KEY `idx_push_channel` (`push_channel`),
  KEY `idx_system_channel` (`system_channel`),
  KEY `idx_push_result` (`push_result`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COMMENT='推送发送记录表'
```

## 表: reddot_status

```sql
CREATE TABLE `reddot_status` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `channel_biz_id` bigint(20) unsigned NOT NULL COMMENT '功能模块ID',
  `channel_data_id` bigint(20) unsigned NOT NULL COMMENT '关联的数据ID',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读:0否,1是',
  `read_time` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_user_channel_data` (`account_id`,`channel_biz_id`,`channel_data_id`) USING BTREE,
  KEY `idx_channel_map_id` (`channel_biz_id`) USING BTREE,
  KEY `idx_data_id` (`channel_data_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=745 DEFAULT CHARSET=utf8mb4 COMMENT='红点状态表'
```

## 表: redemption_code

```sql
CREATE TABLE `redemption_code` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `membership_id` int(11) NOT NULL COMMENT '会员等级1手游会员 2黄金会员 3白金会员',
  `redemption_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1付费用户邀请码 2主播推广码  3官方限时兑换码 4官方不限时码',
  `reward` decimal(5,2) NOT NULL COMMENT '小时',
  `code` varchar(255) NOT NULL COMMENT '兑换码',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '生效时间可以为空',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间可以为空',
  `business_table_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '业务表的主键id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `redemption_group_id` int(11) NOT NULL DEFAULT '1' COMMENT '兑换码组ID',
  `code_desc` varchar(255) DEFAULT NULL COMMENT '兑换码描述信息',
  `max_num` int(11) NOT NULL DEFAULT '0' COMMENT '0表示无限',
  `used_num` int(11) NOT NULL DEFAULT '0' COMMENT '已使用数量',
  `delsign` tinyint(1) NOT NULL DEFAULT '0',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11039 DEFAULT CHARSET=utf8mb4 COMMENT='兑换码（单条）表'
```

## 表: redemption_code_group

```sql
CREATE TABLE `redemption_code_group` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL COMMENT '兑换码组的名称',
  `group_desc` varchar(255) NOT NULL COMMENT '组的描述',
  `business_table_name` varchar(255) DEFAULT NULL COMMENT '业务表表名',
  `start_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `membership_id` int(11) NOT NULL DEFAULT '0' COMMENT '会员等级0不限制 1手游会员 2黄金会员 3白金会员',
  `redemption_type` tinyint(4) NOT NULL DEFAULT '2' COMMENT '0不限 1付费用户邀请码 2主播推广码  3官方限时兑换码 4官方不限时码',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `extra_info` varchar(255) DEFAULT NULL COMMENT '附加信息',
  `code_count` int(11) NOT NULL DEFAULT '0' COMMENT '该组兑换码数量,0无限',
  `max_num_per_code` int(11) NOT NULL DEFAULT '0' COMMENT '每个兑换码可兑换次数，0无限',
  `reward` decimal(5,2) NOT NULL COMMENT '奖励时长',
  `delsign` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0有效1删除',
  `auto_append` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认false,只对redemption_type = 4 类型生效',
  `group_usage_limit_num` int(11) NOT NULL DEFAULT '0' COMMENT '组限制使用次数，0不限制,只对redemption_type = 4 类型生效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COMMENT='兑换码组表'
```

## 表: redemption_code_use_record

```sql
CREATE TABLE `redemption_code_use_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `redemption_code_id` bigint(20) NOT NULL,
  `use_account_id` bigint(20) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `redemption_code_id` (`redemption_code_id`,`use_account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COMMENT='兑换码使用记录表'
```

## 表: router_account_inbound_use_record

```sql
CREATE TABLE `router_account_inbound_use_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `inbound_id` bigint(20) DEFAULT NULL,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `up_traffic` decimal(14,6) DEFAULT NULL COMMENT '上传流量GB',
  `down_traffic` decimal(14,6) DEFAULT NULL COMMENT '下载流量GB',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4
```

## 表: router_client_report_log

```sql
CREATE TABLE `router_client_report_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `vpn_us` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `report_type` int(11) DEFAULT NULL COMMENT '1-黑白名单上报',
  `data` text COMMENT '原始数据',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='客户端上报记录表'
```

## 表: router_connection_liner_specify

```sql
CREATE TABLE `router_connection_liner_specify` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(10) unsigned DEFAULT NULL COMMENT '游戏区服id',
  `liner_id` int(10) unsigned NOT NULL COMMENT 'liner表id',
  `connection_type` int(11) NOT NULL COMMENT '1-游戏 2-影音',
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_region_id`,`liner_id`,`game_id`) USING BTREE,
  KEY `idx_tag_id` (`game_region_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3486 DEFAULT CHARSET=utf8mb4 COMMENT='连接业务指定线路表'
```

## 表: router_node_status

```sql
CREATE TABLE `router_node_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `node_id` bigint(20) NOT NULL COMMENT '节点ID',
  `cpu_usage` double DEFAULT NULL COMMENT 'CPU使用率',
  `cpu_cores` int(11) DEFAULT NULL COMMENT 'CPU核心数',
  `logical_processors` int(11) DEFAULT NULL COMMENT '逻辑处理器数',
  `cpu_speed_mhz` double DEFAULT NULL COMMENT 'CPU频率(MHz)',
  `mem_current` bigint(20) DEFAULT NULL COMMENT '当前内存使用量',
  `mem_total` bigint(20) DEFAULT NULL COMMENT '总内存',
  `swap_current` bigint(20) DEFAULT NULL COMMENT '当前交换分区使用量',
  `swap_total` bigint(20) DEFAULT NULL COMMENT '总交换分区',
  `disk_current` bigint(20) DEFAULT NULL COMMENT '当前磁盘使用量',
  `disk_total` bigint(20) DEFAULT NULL COMMENT '总磁盘空间',
  `xray_state` varchar(50) DEFAULT NULL COMMENT 'Xray状态',
  `xray_error_msg` varchar(500) DEFAULT NULL COMMENT 'Xray错误信息',
  `xray_version` varchar(50) DEFAULT NULL COMMENT 'Xray版本',
  `uptime` bigint(20) DEFAULT NULL COMMENT '系统运行时间',
  `load_avg1` double DEFAULT NULL COMMENT '1分钟负载',
  `load_avg5` double DEFAULT NULL COMMENT '5分钟负载',
  `load_avg15` double DEFAULT NULL COMMENT '15分钟负载',
  `tcp_count` int(11) DEFAULT NULL COMMENT 'TCP连接数',
  `udp_count` int(11) DEFAULT NULL COMMENT 'UDP连接数',
  `net_up_speed` bigint(20) DEFAULT NULL COMMENT '上行速度',
  `net_down_speed` bigint(20) DEFAULT NULL COMMENT '下行速度',
  `net_traffic_sent` bigint(20) DEFAULT NULL COMMENT '发送流量',
  `net_traffic_recv` bigint(20) DEFAULT NULL COMMENT '接收流量',
  `ipv4` varchar(50) DEFAULT NULL COMMENT 'IPv4地址',
  `ipv6` varchar(50) DEFAULT NULL COMMENT 'IPv6地址',
  `app_threads` int(11) DEFAULT NULL COMMENT '应用线程数',
  `app_mem` bigint(20) DEFAULT NULL COMMENT '应用内存使用',
  `app_uptime` bigint(20) DEFAULT NULL COMMENT '应用运行时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_node_id` (`node_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节点状态表'
```

## 表: router_server

```sql
CREATE TABLE `router_server` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ecs_object_id` varchar(64) DEFAULT NULL COMMENT '创建服务器的项目id',
  `ecs_job_id` varchar(64) DEFAULT NULL COMMENT '创建服务器的任务id',
  `ecs_order_id` varchar(64) DEFAULT NULL COMMENT '开启云服务的订单id',
  `ecs_server_id` varchar(64) DEFAULT NULL COMMENT '云服务器id(华为云id)',
  `domain` varchar(64) DEFAULT NULL COMMENT '服务器域名,域名目前没有不用存',
  `public_ip` varchar(64) DEFAULT NULL COMMENT '前端节点 公网ip，暂时业务用不到可以不存',
  `inner_ip` varchar(64) DEFAULT NULL COMMENT '前端节点 内网ip ',
  `vpn_port` int(11) DEFAULT NULL COMMENT 'vpn服务端口',
  `server_region` int(11) DEFAULT NULL COMMENT '前端节点 1.香港节点\n2.广州节点\n3.国内其他节点\n4.海外其他节点',
  `server_name` varchar(64) DEFAULT NULL COMMENT '名称，线路id+小写字母(region)+ip后缀',
  `ecs_create_info` json DEFAULT NULL COMMENT '存入开启esc的请求信息',
  `ecs_server_info` json DEFAULT NULL COMMENT 'esc服务器信息',
  `liner_config_id` int(11) DEFAULT NULL COMMENT '线路配置ID',
  `ecs_config_id` int(11) DEFAULT NULL COMMENT '服务器配置ID',
  `last_heartbeat_time` timestamp NULL DEFAULT NULL COMMENT '最后上报时间',
  `connection_num` int(11) NOT NULL DEFAULT '0' COMMENT '连接数',
  `connection_num_threshold` int(11) NOT NULL DEFAULT '5000' COMMENT '连接数阈值',
  `cpu_use` double(11,2) NOT NULL DEFAULT '0.00' COMMENT '当前cpu占用',
  `cpu_threshold` double(11,2) NOT NULL DEFAULT '70.00' COMMENT 'cpu报警阈值 70',
  `ping_delay_ms` decimal(10,4) NOT NULL DEFAULT '9999.0000' COMMENT 'ping值',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '0.游戏 1.其他',
  `ecs_type` int(11) DEFAULT NULL COMMENT '1-华为 2-阿里云',
  `is_foreign` tinyint(4) DEFAULT NULL COMMENT '0-国内 1-国外',
  `status` int(11) NOT NULL COMMENT '0.异常 1.正常 2.正在创建 3.正在初始化 4.待关闭 5.已退 6待升级 7升级中',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 正常 1: 删除',
  `image_version` decimal(16,4) DEFAULT NULL COMMENT '镜像版本号',
  `charging_mode` varchar(50) NOT NULL COMMENT '华为：prePaid周期付款，postPaid按需付款。 阿里：PostPaid按需付费， PrePaid包年包月',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=149 DEFAULT CHARSET=utf8mb4 COMMENT='esc云服务器表'
```

## 表: router_server_dispatch_relation_bak

```sql
CREATE TABLE `router_server_dispatch_relation_bak` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server_id_from` int(11) DEFAULT NULL,
  `server_id_to` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_key` (`server_id_from`,`server_id_to`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4
```

## 表: router_server_dns

```sql
CREATE TABLE `router_server_dns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `public_ip` varchar(255) NOT NULL,
  `inner_ip` varchar(255) NOT NULL,
  `dns_server_ip` varchar(255) NOT NULL,
  `status` int(11) NOT NULL COMMENT '1-正常 0-不检测 -1-故障',
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  `last_error_time` datetime DEFAULT NULL COMMENT '最后一次检查挂掉时间',
  `last_check_time` datetime DEFAULT NULL COMMENT '最后一次检查时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4
```

## 表: router_server_dns_region_relation

```sql
CREATE TABLE `router_server_dns_region_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dns_id` int(11) NOT NULL,
  `region_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4
```

## 表: router_server_dns_server_relation

```sql
CREATE TABLE `router_server_dns_server_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server_id` int(11) NOT NULL,
  `status` int(11) NOT NULL COMMENT '1-正常 0-不检测 -1-故障',
  `last_error_time` datetime DEFAULT NULL COMMENT '最后一次检查挂掉时间',
  `last_check_time` datetime DEFAULT NULL COMMENT '最后一次检查时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4
```

## 表: router_server_ecs_config

```sql
CREATE TABLE `router_server_ecs_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '创建esc的配置表',
  `region` int(11) NOT NULL COMMENT '前端节点 1.香港节点\n2.广州节点\n',
  `region_str` varchar(50) NOT NULL COMMENT '区域字符串。阿里对应regionId',
  `zone_str` varchar(50) DEFAULT NULL COMMENT '阿里 实例所属的可用区ID',
  `project_id` varchar(50) NOT NULL COMMENT '项目id',
  `security_group_id` text NOT NULL COMMENT '安全组id',
  `subnet_id` varchar(50) NOT NULL COMMENT '子网id。 阿里 虚拟交换机ID vSwitchId ',
  `ip_prefix` varchar(50) NOT NULL COMMENT 'ipv4 地址前缀',
  `image_ref` varchar(50) NOT NULL COMMENT '镜像',
  `flavor_ref` varchar(50) NOT NULL COMMENT '系统规格。阿里 实例的资源规格instanceType ',
  `disk_category` varchar(50) DEFAULT '' COMMENT '磁盘类型',
  `disk_performance_level` varchar(50) DEFAULT NULL COMMENT '磁盘级别',
  `credit_specification` varchar(50) DEFAULT NULL COMMENT '突发性能实例',
  `name` varchar(50) NOT NULL COMMENT '名字',
  `vpc_id` varchar(50) DEFAULT NULL COMMENT '指定已创建VPC的ID',
  `version` int(11) NOT NULL DEFAULT '1' COMMENT '配置版本',
  `remark` text NOT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `charging_mode` varchar(50) NOT NULL COMMENT '华为：prePaid周期付款，postPaid按需付款。 阿里：PostPaid按需付费， PrePaid包年包月',
  `period_type` varchar(50) NOT NULL DEFAULT '' COMMENT 'charging_mode=prePaid起作用，只有month,year两种',
  `delsign` tinyint(4) NOT NULL DEFAULT '1',
  `internet_charge_type` varchar(255) DEFAULT NULL COMMENT '阿里云网络计费类型:PayByBandwidth-按固定带宽计费,PayByTraffic-按使用流量计费',
  `bandwidth` int(11) DEFAULT '100' COMMENT '带宽',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=261006 DEFAULT CHARSET=utf8mb4 COMMENT='esc云服务器表'
```

## 表: router_server_ecs_config_bak

```sql
CREATE TABLE `router_server_ecs_config_bak` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '创建esc的配置表',
  `region` int(11) NOT NULL COMMENT '前端节点 1.香港节点\n2.广州节点\n',
  `region_str` varchar(50) NOT NULL COMMENT '区域字符串。阿里对应regionId',
  `zone_str` varchar(50) DEFAULT NULL COMMENT '阿里 实例所属的可用区ID',
  `project_id` varchar(50) NOT NULL COMMENT '项目id',
  `security_group_id` text NOT NULL COMMENT '安全组id',
  `subnet_id` varchar(50) NOT NULL COMMENT '子网id。 阿里 虚拟交换机ID vSwitchId ',
  `ip_prefix` varchar(50) NOT NULL COMMENT 'ipv4 地址前缀',
  `image_ref` varchar(50) NOT NULL COMMENT '镜像',
  `flavor_ref` varchar(50) NOT NULL COMMENT '系统规格。阿里 实例的资源规格instanceType ',
  `name` varchar(50) NOT NULL COMMENT '名字',
  `vpc_id` varchar(50) DEFAULT NULL COMMENT '指定已创建VPC的ID',
  `version` int(11) NOT NULL DEFAULT '1' COMMENT '配置版本',
  `remark` text NOT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `charging_mode` varchar(50) NOT NULL COMMENT 'prePaid周期付款，postPaid按需付款。 阿里对应字段：instanceChargeType PostPaid:按需付费 ',
  `period_type` varchar(50) NOT NULL DEFAULT '' COMMENT 'charging_mode=prePaid起作用，只有month,year两种',
  `delsign` tinyint(4) NOT NULL DEFAULT '1',
  `internet_charge_type` varchar(255) DEFAULT NULL COMMENT '阿里云网络计费类型:PayByBandwidth-按固定带宽计费,PayByTraffic-按使用流量计费',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=261005 DEFAULT CHARSET=utf8mb4 COMMENT='esc云服务器表'
```

## 表: router_server_ecs_config_relation_改

```sql
CREATE TABLE `router_server_ecs_config_relation_改` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `show_name` varchar(255) DEFAULT NULL COMMENT '网络线路名称',
  `ecs_config_id_from` int(11) DEFAULT NULL COMMENT '国内',
  `ecs_config_id_to` int(11) DEFAULT NULL COMMENT '国外',
  `ecs_type` int(11) DEFAULT '1' COMMENT '1.华为 2.阿里云',
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4
```

## 表: router_server_image

```sql
CREATE TABLE `router_server_image` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `image_ref` varchar(255) DEFAULT NULL COMMENT '镜像ID',
  `name` varchar(255) DEFAULT NULL COMMENT '镜像名称',
  `version` float(16,4) DEFAULT '2.0000' COMMENT '镜像版本号',
  `server_region` int(11) DEFAULT '0' COMMENT '服务器区域id',
  `job_id` varchar(255) DEFAULT NULL,
  `status` tinyint(4) DEFAULT '0' COMMENT '1 创建完成  2 正在创建 ',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 正常, 1: 删除',
  `from_router_server_id` int(11) DEFAULT NULL COMMENT '镜像源的服务器id（阿里镜像新建模式非空）',
  `from_router_server_image_id` int(11) DEFAULT NULL COMMENT '镜像拷贝的原id(阿里镜像拷贝模式非空)',
  PRIMARY KEY (`id`),
  KEY `image_ref` (`image_ref`)
) ENGINE=InnoDB AUTO_INCREMENT=141 DEFAULT CHARSET=utf8mb4
```

## 表: router_server_last_ip_allocation

```sql
CREATE TABLE `router_server_last_ip_allocation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `liner_config_id` int(11) DEFAULT NULL COMMENT 'ECS线路ID',
  `ip_last` int(11) NOT NULL COMMENT 'IP 地址的最后一段',
  `is_using` tinyint(4) DEFAULT '0' COMMENT '0 表示未占用，1 表示占用',
  `is_using_total` tinyint(4) DEFAULT '0' COMMENT '0 表示未占用，1 表示占用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_ip_last` (`liner_config_id`,`ip_last`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18035 DEFAULT CHARSET=utf8mb4
```

## 表: router_server_liner_config

```sql
CREATE TABLE `router_server_liner_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `show_name` varchar(255) DEFAULT NULL COMMENT '线路名称',
  `ecs_type` int(11) NOT NULL COMMENT '运营商 1:华为 2:阿里',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '0.游戏 1.影音',
  `is_global` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0: 国内线路，1:跨国线路',
  `ping_delay_ms` decimal(10,4) NOT NULL DEFAULT '9999.0000' COMMENT 'ping值',
  `liner_type` int(11) NOT NULL DEFAULT '0' COMMENT '0-默认 1-美东 2-美西',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2000126002 DEFAULT CHARSET=utf8mb4 COMMENT='ECS线路配置表'
```

## 表: router_server_liner_config_bak

```sql
CREATE TABLE `router_server_liner_config_bak` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `show_name` varchar(255) DEFAULT NULL COMMENT '线路名称',
  `ecs_type` int(11) NOT NULL COMMENT '运营商 1:华为 2:阿里',
  `ping_delay_ms` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT 'ping值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2000126002 DEFAULT CHARSET=utf8mb4 COMMENT='ECS线路配置表'
```

## 表: router_server_liner_ecs_config_relation

```sql
CREATE TABLE `router_server_liner_ecs_config_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `liner_config_id` int(11) DEFAULT NULL COMMENT '线路配置表ID',
  `ecs_config_id` int(11) DEFAULT NULL COMMENT 'ECS配置表ID',
  `server_region_id` int(11) DEFAULT NULL COMMENT '服务器区域,冗余字段 ',
  `liner_type` int(11) DEFAULT NULL COMMENT '线路类型 0: 国内 1:国外 2:中间跳转隐藏层',
  `ecs_type` int(11) DEFAULT NULL COMMENT '运营商，可以放到router_server_ecs_config ',
  `is_foreign` int(11) DEFAULT NULL COMMENT '是否国外，可以放到router_server_ecs_config',
  `ip_prefix` varchar(50) NOT NULL COMMENT 'ipv4 地址前缀',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ip_prefix` (`ip_prefix`) USING BTREE,
  KEY `liner_config_id` (`liner_config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2050261002 DEFAULT CHARSET=utf8mb4 COMMENT='线路配置关联表'
```

## 表: router_server_liner_ecs_config_relation_bak

```sql
CREATE TABLE `router_server_liner_ecs_config_relation_bak` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `liner_config_id` int(11) DEFAULT NULL COMMENT '线路配置表ID',
  `ecs_config_id` int(11) DEFAULT NULL COMMENT 'ECS配置表ID',
  `server_region_id` int(11) DEFAULT NULL COMMENT '服务器区域,冗余字段 ',
  `liner_type` int(11) DEFAULT NULL COMMENT '线路类型 0: 国内 1:国外 2:中间跳转隐藏层',
  `ecs_type` int(11) DEFAULT NULL COMMENT '运营商，可以放到router_server_ecs_config ',
  `is_foreign` int(11) DEFAULT NULL COMMENT '是否国外，可以放到router_server_ecs_config',
  `ip_prefix` varchar(50) NOT NULL COMMENT 'ipv4 地址前缀',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ip_prefix` (`ip_prefix`) USING BTREE,
  KEY `liner_config_id` (`liner_config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2040261002 DEFAULT CHARSET=utf8mb4 COMMENT='线路配置关联表'
```

## 表: router_server_liner_ecs_config_relation_bak_bak

```sql
CREATE TABLE `router_server_liner_ecs_config_relation_bak_bak` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `liner_config_id` int(11) DEFAULT NULL COMMENT '线路配置表ID',
  `ecs_config_id` int(11) DEFAULT NULL COMMENT 'ECS配置表ID',
  `server_region_id` int(11) DEFAULT NULL COMMENT '服务器区域,冗余字段 ',
  `liner_type` int(11) DEFAULT NULL COMMENT '线路类型 0: 国内 1:国外 2:中间跳转隐藏层',
  `ecs_type` int(11) DEFAULT NULL COMMENT '运营商，可以放到router_server_ecs_config ',
  `is_foreign` int(11) DEFAULT NULL COMMENT '是否国外，可以放到router_server_ecs_config',
  `ip_prefix` varchar(50) NOT NULL COMMENT 'ipv4 地址前缀',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ip_prefix` (`ip_prefix`) USING BTREE,
  KEY `liner_config_id` (`liner_config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COMMENT='线路配置关联表'
```

## 表: router_server_liner_server_relation

```sql
CREATE TABLE `router_server_liner_server_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `liner_ecs_config_relation_id` int(11) DEFAULT NULL COMMENT '线路配置关联表ID',
  `liner_config_id` int(11) DEFAULT NULL COMMENT '线路ID，冗余字段',
  `server_id` int(11) DEFAULT NULL COMMENT 'router_server表服务器实例ID',
  `last_ip` int(11) DEFAULT NULL COMMENT 'IP 地址的最后一段, 相同ilner 相同last_id 是一组线路服务器实例',
  `status` int(11) NOT NULL COMMENT '0.异常 1.正常 2.正在创建 3.正在初始化 4.待关闭 5.已退 6.待升级 7.升级中',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 正常 1: 删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=107 DEFAULT CHARSET=utf8mb4 COMMENT='节点线路表'
```

## 表: router_server_online_state

```sql
CREATE TABLE `router_server_online_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `server_id` int(11) NOT NULL COMMENT '服务器ID',
  `online` bigint(20) NOT NULL COMMENT '在线数量',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `server_id` (`server_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=81178 DEFAULT CHARSET=utf8mb4 COMMENT='服务器在线状态表'
```

## 表: router_server_online_user_state

```sql
CREATE TABLE `router_server_online_user_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `assigned_ipv4` varchar(15) DEFAULT NULL COMMENT '分配的IPv4地址',
  `assigned_ipv6` varchar(45) DEFAULT NULL COMMENT '分配的IPv6地址',
  `bytes_received` bigint(20) NOT NULL COMMENT '接收字节数',
  `bytes_received_offset` bigint(20) NOT NULL DEFAULT '0',
  `bytes_sent` bigint(20) NOT NULL COMMENT '发送字节数',
  `bytes_sent_offset` bigint(20) NOT NULL DEFAULT '0',
  `total_traffic` bigint(20) DEFAULT '0' COMMENT '总流量',
  `nickname` varchar(255) DEFAULT NULL COMMENT 'account昵称',
  `cid` bigint(20) NOT NULL COMMENT '连接ID',
  `ipport` varchar(50) DEFAULT NULL COMMENT 'IP和端口',
  `method` varchar(50) DEFAULT NULL COMMENT '连接方法',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  `username` varchar(100) DEFAULT NULL COMMENT '用户名',
  `account_id` bigint(20) NOT NULL COMMENT '账户ID',
  `vpn_u` varchar(100) DEFAULT NULL COMMENT 'VPN唯一标识',
  `server_id` int(11) NOT NULL COMMENT '服务器id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `timestamp` (`timestamp`) USING BTREE,
  KEY `account_id` (`account_id`) USING BTREE,
  KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=79238 DEFAULT CHARSET=utf8mb4 COMMENT='服务器在线用户状态表'
```

## 表: router_server_region

```sql
CREATE TABLE `router_server_region` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `region_name` varchar(50) DEFAULT NULL,
  `show_name` varchar(255) DEFAULT NULL COMMENT '线路名称',
  `longitude` double(16,4) DEFAULT NULL COMMENT '经度',
  `latitude` double(16,4) DEFAULT NULL COMMENT '纬度',
  `ecs_type` int(11) NOT NULL COMMENT '运营商 1:华为 2:阿里',
  `is_foreign` tinyint(4) NOT NULL COMMENT '0-国内 1-国外',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` double(4,0) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=********** DEFAULT CHARSET=utf8mb4 COMMENT='ECS区域表'
```

## 表: router_server_region_copy

```sql
CREATE TABLE `router_server_region_copy` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `region_name` varchar(50) DEFAULT NULL,
  `show_name` varchar(255) DEFAULT NULL COMMENT '线路名称',
  `longitude` double(16,4) DEFAULT NULL COMMENT '经度',
  `latitude` double(16,4) DEFAULT NULL COMMENT '纬度',
  `ecs_type` int(11) NOT NULL COMMENT '运营商 1:华为 2:阿里',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` double(4,0) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=********** DEFAULT CHARSET=utf8mb4 COMMENT='ECS区域表'
```

## 表: router_server_relation

```sql
CREATE TABLE `router_server_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server_id_from` int(11) DEFAULT NULL,
  `public_ip_from` varchar(255) DEFAULT NULL COMMENT '前端节点 公网ip，业务暂时用不到可以不存',
  `inner_ip_from` varchar(255) DEFAULT NULL COMMENT '前端节点 内网ip ',
  `region_from` int(11) DEFAULT NULL COMMENT '前端节点 1.香港节点\n2.广州节点\n3.国内其他节点\n4.海外其他节点',
  `server_id_to` int(11) DEFAULT NULL,
  `public_ip_to` varchar(255) DEFAULT NULL COMMENT '转发节点 公网ip，业务暂时用不到可以不存',
  `inner_ip_to` varchar(255) DEFAULT NULL COMMENT '转发节点 内网ip',
  `region_to` int(11) DEFAULT NULL COMMENT '转发节点 1.香港节点\n2.广州节点\n3.国内其他节点\n4.海外其他节点',
  `show_name` varchar(255) DEFAULT NULL COMMENT '名称',
  `status` int(11) DEFAULT '1' COMMENT '0.异常 1.正常 2.正在创建 3.正在初始化 4.待关闭 5.已退',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `line_id` int(11) DEFAULT NULL COMMENT '网络路线id,  router_server_ecs_config 表的id',
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4
```

## 表: router_xray_inbound

```sql
CREATE TABLE `router_xray_inbound` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `xray_id` int(11) DEFAULT NULL COMMENT '3x xray id',
  `server_id` bigint(20) DEFAULT NULL COMMENT '机器id',
  `account_id` bigint(20) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `protocol` int(11) DEFAULT NULL COMMENT '协议\n1.vmess\n2.vless\n3.shadowsocks\n4.socks\n5.http\n6.trojan\n7.wireguard',
  `listen_ip` varchar(255) DEFAULT NULL COMMENT '监听',
  `port` int(11) DEFAULT NULL COMMENT '端口',
  `total_traffic` decimal(14,6) DEFAULT NULL COMMENT '总流量 GB',
  `expired_time` datetime DEFAULT NULL COMMENT '到期时间',
  `status` int(11) DEFAULT NULL COMMENT '状态\n0-xray已回收\n1-已创建未连接\n2-连接中\n3-未连接待回收',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountid_idx` (`account_id`) USING BTREE,
  KEY `serverid_idx` (`server_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4
```

## 表: router_xray_inbound_shadowsocks_extend

```sql
CREATE TABLE `router_xray_inbound_shadowsocks_extend` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inbound_id` bigint(20) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL COMMENT 'id',
  `encry_method` varchar(255) DEFAULT NULL,
  `nonce` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inboundid_idx` (`inbound_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4
```

## 表: router_xray_inbound_socks_extend

```sql
CREATE TABLE `router_xray_inbound_socks_extend` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inbound_id` bigint(20) DEFAULT NULL,
  `user` varchar(255) DEFAULT NULL COMMENT 'id',
  `pass` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inboundid_idx` (`inbound_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4
```

## 表: router_xray_inbound_trojan_extend

```sql
CREATE TABLE `router_xray_inbound_trojan_extend` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inbound_id` bigint(20) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL COMMENT 'id',
  `type` varchar(255) DEFAULT NULL,
  `security` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4
```

## 表: router_xray_inbound_vless_extend

```sql
CREATE TABLE `router_xray_inbound_vless_extend` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inbound_id` bigint(20) DEFAULT NULL,
  `client_id` varchar(255) DEFAULT NULL COMMENT 'id',
  `type` varchar(255) DEFAULT NULL,
  `security` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4
```

## 表: router_xray_inbound_vmess_extend

```sql
CREATE TABLE `router_xray_inbound_vmess_extend` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inbound_id` bigint(20) DEFAULT NULL,
  `client_id` varchar(255) DEFAULT NULL COMMENT 'id',
  `type` varchar(255) DEFAULT NULL,
  `security` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `net` varchar(255) DEFAULT NULL,
  `tls` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4
```

## 表: router_xray_inbound_wireguard_extend

```sql
CREATE TABLE `router_xray_inbound_wireguard_extend` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inbound_id` bigint(20) DEFAULT NULL COMMENT 'inbound id',
  `wg_private_key` varchar(255) DEFAULT NULL,
  `wg_public_key` varchar(255) DEFAULT NULL,
  `peer_private_key` varchar(255) DEFAULT NULL,
  `peer_public_key` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4
```

## 表: transaction

```sql
CREATE TABLE `transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL COMMENT '用户id',
  `product_id` varchar(64) NOT NULL COMMENT '付费产品id',
  `parent_identifier` varchar(64) DEFAULT NULL COMMENT '续订关联的平台订单id',
  `identifier` varchar(255) NOT NULL COMMENT '平台订单id',
  `device_uuid` varchar(64) DEFAULT NULL COMMENT '用户设备uuid',
  `device_name` varchar(64) DEFAULT NULL COMMENT '用户设备名称',
  `system_version` varchar(64) DEFAULT NULL COMMENT '用户系统版本',
  `model` varchar(64) DEFAULT NULL COMMENT '用户设备号',
  `bvrs` varchar(15) DEFAULT NULL COMMENT 'app版本',
  `ip` varchar(63) DEFAULT NULL COMMENT '支付ip',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `download_id` bigint(20) DEFAULT NULL,
  `receipt_data` text COMMENT '平台回调原始信息',
  `receipt_json` json DEFAULT NULL COMMENT '平台回调json信息',
  `signed_payload` text COMMENT '苹果订阅回调原始信息',
  `signed_payload_json` json DEFAULT NULL COMMENT '苹果内购回调json信息',
  `status_code` int(11) NOT NULL DEFAULT '0' COMMENT '0 支付失败, 1 生成订单未支付， 2 支付成功， 3 取消支付',
  `sand_box` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0:线上环境 1:沙盒环境,测试环境',
  `country_code` char(2) DEFAULT NULL,
  `province` varchar(45) DEFAULT NULL,
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 正常 1: 删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_identifier` (`identifier`) USING BTREE COMMENT 'DO NOT DELETE',
  KEY `account_id` (`account_id`) USING BTREE,
  KEY `idx_transaction_date` (`create_time`) USING BTREE,
  KEY `idx_transaction_productid` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3316 DEFAULT CHARSET=utf8mb4
```

## 表: transaction_googleplay_callback_log

```sql
CREATE TABLE `transaction_googleplay_callback_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` varchar(128) DEFAULT NULL COMMENT 'googleplay的消息id',
  `transaction_id` bigint(20) DEFAULT NULL COMMENT 'transaction表id',
  `callback_type` varchar(100) DEFAULT NULL COMMENT '回调的事件类型',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `publish_time` timestamp NULL DEFAULT NULL COMMENT 'googleplay的消息发布时间',
  `subscription` varchar(128) DEFAULT NULL COMMENT 'pub/sub队列名',
  `package_name` varchar(255) DEFAULT NULL COMMENT '包名',
  `event_time_millis` timestamp NULL DEFAULT NULL COMMENT '事件真实发生时间',
  `ot_notification_type` int(11) DEFAULT NULL COMMENT '一次性购买通知类型',
  `ot_purchase_token` text COMMENT '一次性购买token',
  `ot_sku` varchar(128) DEFAULT NULL COMMENT '一次性购买产品名',
  `is_success` int(11) DEFAULT NULL COMMENT '0-失败 1-成功',
  `failed_reason` text COMMENT '失败原因',
  `google_order_id` varchar(128) DEFAULT NULL COMMENT 'google订单id',
  `void_purchase_token` text COMMENT '取消/退款通知token',
  `void_product_type` int(11) DEFAULT NULL,
  `void_refund_type` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `message_id_idx` (`message_id`) USING BTREE,
  KEY `idx_transaction_date` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4
```

## 表: transaction_googleplay_log

```sql
CREATE TABLE `transaction_googleplay_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` varchar(128) DEFAULT NULL COMMENT 'googleplay的消息id',
  `transaction_id` bigint(20) DEFAULT NULL COMMENT 'transaction表id',
  `transaction_status` int(11) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `message_id_idx` (`message_id`) USING BTREE,
  KEY `idx_transaction_date` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=210 DEFAULT CHARSET=utf8mb4
```

## 表: transaction_log

```sql
CREATE TABLE `transaction_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `platform` varchar(50) NOT NULL COMMENT '平台',
  `identifier` varchar(64) DEFAULT NULL COMMENT '平台订单id',
  `callback_type` varchar(100) DEFAULT NULL COMMENT '回调的事件类型',
  `receipt_data` text COMMENT '平台回调原始信息',
  `receipt_json` json DEFAULT NULL COMMENT '平台回调json信息',
  `signed_payload` text COMMENT '苹果订阅回调原始信息',
  `signed_payload_json` json DEFAULT NULL COMMENT '苹果内购回调json信息',
  `sand_box` tinyint(1) DEFAULT '0' COMMENT '0:线上环境 1:沙盒环境,测试环境',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_transaction_date` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2957 DEFAULT CHARSET=utf8mb4
```

## 表: transaction_product

```sql
CREATE TABLE `transaction_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `platform` varchar(45) NOT NULL COMMENT '平台',
  `product_id` varchar(100) NOT NULL COMMENT '产品',
  `membership_id` int(11) DEFAULT NULL,
  `product_name` varchar(45) DEFAULT NULL,
  `currency` varchar(10) DEFAULT NULL COMMENT '货币类型',
  `price` int(11) NOT NULL COMMENT '价格',
  `first_price` int(11) DEFAULT NULL,
  `first_pay_type` int(11) NOT NULL DEFAULT '0' COMMENT '0 正常 1 首次',
  `income` float(11,2) NOT NULL COMMENT '收入',
  `income_dm` decimal(11,2) NOT NULL COMMENT '收入',
  `duration_days` int(11) NOT NULL COMMENT '增加天数',
  `discount_duration_days` int(11) NOT NULL COMMENT '额外优惠天数',
  `order_type` int(11) NOT NULL COMMENT '0:直接购买，1:订阅',
  `subscription_type` int(11) DEFAULT NULL COMMENT '1: 月, 2: 季度, 3: 年',
  `remark` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `name_UNIQUE` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=60009 DEFAULT CHARSET=utf8mb4 COMMENT='平台订阅充值产品表	'
```

## 表: transaction_refund_log

```sql
CREATE TABLE `transaction_refund_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `transaction_id` bigint(20) NOT NULL COMMENT '关联的订单ID',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `refund_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '退款时间',
  `reason` text COMMENT '退款原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `order_id` (`transaction_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4
```

## 表: vpn_auto_detect_ip_blacklist

```sql
CREATE TABLE `vpn_auto_detect_ip_blacklist` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `ip` varchar(32) DEFAULT NULL,
  `protocol` varchar(20) NOT NULL DEFAULT 'tcp' COMMENT '协议：tcp,udp',
  `is_black` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1: 黑，0: 白',
  `status` int(4) NOT NULL DEFAULT '0' COMMENT '处理状态 0-未处理 1-已处理',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `system_channel` tinyint(4) DEFAULT NULL COMMENT '1 苹果 2 安卓 3 pc 4 鸿蒙',
  PRIMARY KEY (`id`),
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`ip`,`protocol`,`is_black`,`system_channel`) USING BTREE,
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=550077 DEFAULT CHARSET=utf8mb4 COMMENT='ip黑名单自动获取'
```

## 表: vpn_auto_detect_ip_blacklist_record

```sql
CREATE TABLE `vpn_auto_detect_ip_blacklist_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `remoteip` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `bytes_received` bigint(20) DEFAULT '0' COMMENT '流量',
  `protocol` varchar(20) NOT NULL DEFAULT 'tcp' COMMENT '协议：tcp,udp',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip_name` (`remoteip`,`username`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT=' vpn自动扫描到的黑名单记录表'
```

## 表: vpn_auto_dns_node_conf

```sql
CREATE TABLE `vpn_auto_dns_node_conf` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inner_ip` varchar(255) NOT NULL,
  `public_ip` varchar(255) NOT NULL,
  `game_region_id` int(11) NOT NULL COMMENT '游戏区分id',
  `remark` varchar(255) DEFAULT NULL COMMENT '描述',
  `last_ping` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '上次通知数据库时间戳',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7006 DEFAULT CHARSET=utf8mb4 COMMENT='自动解析域名节点配置表'
```

## 表: vpn_auto_domain_ip

```sql
CREATE TABLE `vpn_auto_domain_ip` (
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `ip` varchar(255) NOT NULL COMMENT '解析IP',
  `game_region_id` int(11) NOT NULL COMMENT '游戏区服id',
  `status` int(4) NOT NULL DEFAULT '0' COMMENT '处理状态 0-未处理 1-已处理',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`domain_name`,`ip`,`game_region_id`),
  KEY `status` (`status`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='域名解析IP结果表'
```

## 表: vpn_auto_domain_ip_resolve

```sql
CREATE TABLE `vpn_auto_domain_ip_resolve` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `ip` varchar(255) NOT NULL COMMENT '解析IP',
  `game_id` bigint(20) NOT NULL COMMENT '游戏id',
  `game_region_id` int(11) NOT NULL COMMENT '游戏区服id',
  `status` int(4) NOT NULL DEFAULT '0' COMMENT '处理状态 0-未处理 1-已处理',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `domain_name` (`domain_name`,`ip`,`game_id`,`game_region_id`) USING BTREE,
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=102642410 DEFAULT CHARSET=utf8mb4 COMMENT='域名解析IP结果表'
```

## 表: vpn_callback_log

```sql
CREATE TABLE `vpn_callback_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `opt_value` varchar(32) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `log_str` varchar(1024) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `username` (`username`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12800 DEFAULT CHARSET=utf8mb4
```

## 表: vpn_client_report_blackwhitelist

```sql
CREATE TABLE `vpn_client_report_blackwhitelist` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) DEFAULT NULL,
  `ip_mask` varchar(255) DEFAULT NULL,
  `is_black` tinyint(4) DEFAULT NULL,
  `protocol` varchar(32) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT '0',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip` (`ip`,`game_id`,`game_region_id`) USING BTREE,
  KEY `status` (`status`),
  KEY `ip_mask` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=199 DEFAULT CHARSET=utf8mb4 COMMENT='客户端上报黑白名单表'
```

## 表: vpn_connection

```sql
CREATE TABLE `vpn_connection` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `device_uuid` varchar(255) DEFAULT NULL,
  `server_id` int(11) DEFAULT NULL,
  `status` int(11) DEFAULT NULL COMMENT '连接状态 0-已断开 1-初始化 2-已连接 3-断开中',
  `start_time` bigint(20) DEFAULT NULL,
  `end_time` bigint(20) DEFAULT NULL,
  `up_traffic` bigint(20) DEFAULT NULL COMMENT '上传流量bytes',
  `down_traffic` bigint(20) DEFAULT NULL COMMENT '下载流量bytes',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `delsign` tinyint(4) DEFAULT '0',
  `vpn_u` varchar(255) DEFAULT NULL,
  `vpn_s` varchar(255) DEFAULT NULL,
  `vpn_us` varchar(255) DEFAULT NULL,
  `apply_record_id` bigint(20) DEFAULT NULL,
  `close_record_id` bigint(20) DEFAULT NULL,
  `close_type` int(11) DEFAULT NULL COMMENT '1-用户主动断开连接\n2-订阅过期系统发起踢人\n3-超过设备数量限制\n4-前端传递 前端状态和后端状态不同步\n5-用户弱网等vpn服务关闭连接\n6-1分钟内还没有收到open的回调\n7-后台管理系统发起的踢人/封号',
  `vpn_assignedipv4` varchar(255) DEFAULT NULL,
  `vpn_ip` varchar(255) DEFAULT NULL,
  `vpn_platform` varchar(255) DEFAULT NULL,
  `vpn_protocol` varchar(255) DEFAULT NULL,
  `vpn_version` varchar(255) DEFAULT NULL,
  `vpn_seq_ts` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `server_id_idx` (`server_id`) USING BTREE,
  KEY `device_uuid_idx` (`device_uuid`) USING BTREE,
  KEY `vpn_us_idx` (`vpn_us`) USING BTREE,
  KEY `idx_create_time_account_id` (`create_time`,`account_id`) USING BTREE,
  KEY `idx_start_end_time` (`start_time`,`end_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6128 DEFAULT CHARSET=utf8mb4
```

## 表: vpn_connection_apply_blackwhitelist_ignore

```sql
CREATE TABLE `vpn_connection_apply_blackwhitelist_ignore` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) NOT NULL,
  `game_region_id` int(11) DEFAULT NULL COMMENT '如果为null表示屏蔽这个游戏的所有区服，如果不为null表示只屏蔽这个游戏的这个区服。',
  `ip_mask` varchar(255) DEFAULT NULL COMMENT '如果为null表示忽略所有，如果不为null表示只忽略这个网段。\n要写成 xx.xx.xx.xx ************* 这种格式 不要写 xx.xx.xx.xx/24',
  `type` int(11) NOT NULL COMMENT '1-黑名单相关屏蔽\n2-白名单相关屏蔽',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='Apply接口黑白名单最终过滤表'
```

## 表: vpn_connection_apply_record

```sql
CREATE TABLE `vpn_connection_apply_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `device_uuid` varchar(255) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL COMMENT '加速游戏id',
  `game_region_id` bigint(20) DEFAULT NULL COMMENT '加速游戏服id',
  `account_pay_status` int(11) DEFAULT NULL COMMENT '用户支付状态 1-free用户 2-订阅用户',
  `connection_type` int(11) DEFAULT NULL COMMENT '连接业务类型 1-游戏 2-。。',
  `server_id` int(11) DEFAULT NULL,
  `vpn_u` varchar(255) DEFAULT NULL,
  `vpn_s` varchar(255) DEFAULT NULL,
  `vpn_us` varchar(255) DEFAULT NULL,
  `vpn_p` varchar(255) DEFAULT NULL,
  `fail_reason` varchar(1024) DEFAULT NULL COMMENT '连接失败原因',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `origin_ip` varchar(255) DEFAULT NULL,
  `country_code` varchar(255) DEFAULT NULL,
  `system_channel` int(11) DEFAULT NULL,
  `account_membership_type` int(11) DEFAULT NULL,
  `origin_game_region_id` bigint(20) DEFAULT NULL COMMENT '原始访问的gameRegionId',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6148 DEFAULT CHARSET=utf8mb4
```

## 表: vpn_connection_close_record

```sql
CREATE TABLE `vpn_connection_close_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `device_uuid` varchar(255) DEFAULT NULL,
  `server_id` int(11) DEFAULT NULL,
  `close_type` int(11) DEFAULT NULL,
  `vpn_u` varchar(255) DEFAULT NULL,
  `vpn_s` varchar(255) DEFAULT NULL,
  `fail_reason` varchar(1024) DEFAULT NULL COMMENT '连接失败原因',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=548 DEFAULT CHARSET=utf8mb4
```

## 表: vpn_default_config

```sql
CREATE TABLE `vpn_default_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `config` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4
```

