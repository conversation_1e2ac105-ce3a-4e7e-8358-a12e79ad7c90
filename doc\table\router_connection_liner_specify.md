## 表: router_connection_liner_specify

```sql
CREATE TABLE `router_connection_liner_specify` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(10) unsigned DEFAULT NULL COMMENT '游戏区服id',
  `liner_id` int(10) unsigned NOT NULL COMMENT 'liner表id',
  `connection_type` int(11) NOT NULL COMMENT '1-游戏 2-影音',
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_game_tag` (`game_region_id`,`liner_id`,`game_id`) USING BTREE,
  KEY `idx_tag_id` (`game_region_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3486 DEFAULT CHARSET=utf8mb4 COMMENT='连接业务指定线路表'
```

