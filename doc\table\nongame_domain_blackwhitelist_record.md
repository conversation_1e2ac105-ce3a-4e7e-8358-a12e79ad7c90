## 表: nongame_domain_blackwhitelist_record

```sql
CREATE TABLE `nongame_domain_blackwhitelist_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `channel` int(11) DEFAULT NULL,
  `channel_status` int(11) DEFAULT NULL,
  `is_foreign` int(11) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `game_id` (`game_id`,`game_region_id`,`domain`,`channel`,`protocol`,`is_foreign`) USING BTREE,
  KEY `domain` (`domain`)
) ENGINE=InnoDB AUTO_INCREMENT=17485 DEFAULT CHARSET=utf8mb4
```

