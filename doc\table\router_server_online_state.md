## 表: router_server_online_state

```sql
CREATE TABLE `router_server_online_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `server_id` int(11) NOT NULL COMMENT '服务器ID',
  `online` bigint(20) NOT NULL COMMENT '在线数量',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `server_id` (`server_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=81178 DEFAULT CHARSET=utf8mb4 COMMENT='服务器在线状态表'
```

