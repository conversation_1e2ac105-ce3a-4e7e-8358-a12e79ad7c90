## 表: router_xray_inbound_wireguard_extend

```sql
CREATE TABLE `router_xray_inbound_wireguard_extend` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inbound_id` bigint(20) DEFAULT NULL COMMENT 'inbound id',
  `wg_private_key` varchar(255) DEFAULT NULL,
  `wg_public_key` varchar(255) DEFAULT NULL,
  `peer_private_key` varchar(255) DEFAULT NULL,
  `peer_public_key` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4
```

