## 表: vpn_auto_domain_ip

```sql
CREATE TABLE `vpn_auto_domain_ip` (
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `ip` varchar(255) NOT NULL COMMENT '解析IP',
  `game_region_id` int(11) NOT NULL COMMENT '游戏区服id',
  `status` int(4) NOT NULL DEFAULT '0' COMMENT '处理状态 0-未处理 1-已处理',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`domain_name`,`ip`,`game_region_id`),
  KEY `status` (`status`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='域名解析IP结果表'
```

