## 表: game_tag

```sql
CREATE TABLE `game_tag` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `en_name` varchar(50) DEFAULT NULL,
  `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:手机游戏类别 ,1:苹果爬取类别,2:android爬取类别，3:pc游戏库类别，4:pc游戏启动类型',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，降序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_name` (`name`,`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=30009 DEFAULT CHARSET=utf8mb4 COMMENT='游戏标签表'
```

