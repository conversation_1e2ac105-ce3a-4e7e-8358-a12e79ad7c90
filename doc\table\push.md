## 表: push

```sql
CREATE TABLE `push` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `push_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '发送类别：1-指定用户列表',
  `push_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '发送状态：0-未发送 1-发送中 2-发送成功 3-发送失败',
  `delay_send_time` datetime DEFAULT NULL COMMENT '计划发送时间(用于延迟发送)',
  `send_progress` int(11) NOT NULL DEFAULT '0' COMMENT '发送进度(苹果平台每次不超过100)',
  `send_total` int(11) NOT NULL DEFAULT '0' COMMENT '发送总数',
  `account_list` text COMMENT '用户账号列表(逗号分隔)',
  `title` varchar(255) NOT NULL COMMENT '推送标题',
  `description` text COMMENT '推送内容',
  `admin_user_id` bigint(20) NOT NULL COMMENT '操作管理员ID',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_push_status` (`push_status`),
  KEY `idx_send_time` (`delay_send_time`),
  KEY `idx_admin_user_id` (`admin_user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='推送任务表'
```

