## 表: feedback_image

```sql
CREATE TABLE `feedback_image` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `feedback_id` bigint(20) unsigned NOT NULL COMMENT '反馈ID',
  `url` varchar(255) NOT NULL COMMENT '图片URL',
  `size` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '图片大小(字节)',
  `format` varchar(10) NOT NULL COMMENT '图片格式',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_feedback_id` (`feedback_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='反馈图片表'
```

