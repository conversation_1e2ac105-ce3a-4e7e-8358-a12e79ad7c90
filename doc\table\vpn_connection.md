## 表: vpn_connection

```sql
CREATE TABLE `vpn_connection` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `device_uuid` varchar(255) DEFAULT NULL,
  `server_id` int(11) DEFAULT NULL,
  `status` int(11) DEFAULT NULL COMMENT '连接状态 0-已断开 1-初始化 2-已连接 3-断开中',
  `start_time` bigint(20) DEFAULT NULL,
  `end_time` bigint(20) DEFAULT NULL,
  `up_traffic` bigint(20) DEFAULT NULL COMMENT '上传流量bytes',
  `down_traffic` bigint(20) DEFAULT NULL COMMENT '下载流量bytes',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `delsign` tinyint(4) DEFAULT '0',
  `vpn_u` varchar(255) DEFAULT NULL,
  `vpn_s` varchar(255) DEFAULT NULL,
  `vpn_us` varchar(255) DEFAULT NULL,
  `apply_record_id` bigint(20) DEFAULT NULL,
  `close_record_id` bigint(20) DEFAULT NULL,
  `close_type` int(11) DEFAULT NULL COMMENT '1-用户主动断开连接\n2-订阅过期系统发起踢人\n3-超过设备数量限制\n4-前端传递 前端状态和后端状态不同步\n5-用户弱网等vpn服务关闭连接\n6-1分钟内还没有收到open的回调\n7-后台管理系统发起的踢人/封号',
  `vpn_assignedipv4` varchar(255) DEFAULT NULL,
  `vpn_ip` varchar(255) DEFAULT NULL,
  `vpn_platform` varchar(255) DEFAULT NULL,
  `vpn_protocol` varchar(255) DEFAULT NULL,
  `vpn_version` varchar(255) DEFAULT NULL,
  `vpn_seq_ts` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `server_id_idx` (`server_id`) USING BTREE,
  KEY `device_uuid_idx` (`device_uuid`) USING BTREE,
  KEY `vpn_us_idx` (`vpn_us`) USING BTREE,
  KEY `idx_create_time_account_id` (`create_time`,`account_id`) USING BTREE,
  KEY `idx_start_end_time` (`start_time`,`end_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6128 DEFAULT CHARSET=utf8mb4
```

