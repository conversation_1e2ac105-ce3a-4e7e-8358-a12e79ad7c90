## 表: vpn_connection_apply_blackwhitelist_ignore

```sql
CREATE TABLE `vpn_connection_apply_blackwhitelist_ignore` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `game_id` bigint(20) NOT NULL,
  `game_region_id` int(11) DEFAULT NULL COMMENT '如果为null表示屏蔽这个游戏的所有区服，如果不为null表示只屏蔽这个游戏的这个区服。',
  `ip_mask` varchar(255) DEFAULT NULL COMMENT '如果为null表示忽略所有，如果不为null表示只忽略这个网段。\n要写成 xx.xx.xx.xx ************* 这种格式 不要写 xx.xx.xx.xx/24',
  `type` int(11) NOT NULL COMMENT '1-黑名单相关屏蔽\n2-白名单相关屏蔽',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='Apply接口黑白名单最终过滤表'
```

