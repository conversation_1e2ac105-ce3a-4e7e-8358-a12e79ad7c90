## 表: router_server_online_user_state

```sql
CREATE TABLE `router_server_online_user_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `assigned_ipv4` varchar(15) DEFAULT NULL COMMENT '分配的IPv4地址',
  `assigned_ipv6` varchar(45) DEFAULT NULL COMMENT '分配的IPv6地址',
  `bytes_received` bigint(20) NOT NULL COMMENT '接收字节数',
  `bytes_received_offset` bigint(20) NOT NULL DEFAULT '0',
  `bytes_sent` bigint(20) NOT NULL COMMENT '发送字节数',
  `bytes_sent_offset` bigint(20) NOT NULL DEFAULT '0',
  `total_traffic` bigint(20) DEFAULT '0' COMMENT '总流量',
  `nickname` varchar(255) DEFAULT NULL COMMENT 'account昵称',
  `cid` bigint(20) NOT NULL COMMENT '连接ID',
  `ipport` varchar(50) DEFAULT NULL COMMENT 'IP和端口',
  `method` varchar(50) DEFAULT NULL COMMENT '连接方法',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  `username` varchar(100) DEFAULT NULL COMMENT '用户名',
  `account_id` bigint(20) NOT NULL COMMENT '账户ID',
  `vpn_u` varchar(100) DEFAULT NULL COMMENT 'VPN唯一标识',
  `server_id` int(11) NOT NULL COMMENT '服务器id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `timestamp` (`timestamp`) USING BTREE,
  KEY `account_id` (`account_id`) USING BTREE,
  KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=79238 DEFAULT CHARSET=utf8mb4 COMMENT='服务器在线用户状态表'
```

