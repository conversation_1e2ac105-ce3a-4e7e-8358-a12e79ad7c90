## 表: channel_biz

```sql
CREATE TABLE `channel_biz` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `channel_type` tinyint(4) NOT NULL COMMENT '频道类型:0公告,1系统消息,2活动通知等',
  `channel_table` varchar(50) NOT NULL COMMENT '模块唯一标识符',
  `desc` varchar(100) NOT NULL COMMENT '描述',
  `delsign` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_code` (`channel_table`) USING BTREE,
  KEY `idx_channel_type` (`channel_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='功能模块映射表'
```

