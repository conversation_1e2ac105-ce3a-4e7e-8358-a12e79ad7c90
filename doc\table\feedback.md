## 表: feedback

```sql
CREATE TABLE `feedback` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '反馈类型：0游戏反馈，1问题反馈，2加速异常，3缺少游戏或区服，4会员充值，5优化建议，6加速效果不理想, 7网络环境改善, 8加速达不到预期, 9没有想要的游戏, 10同类产品更好, 11其他问题\r\n',
  `content` text NOT NULL COMMENT '反馈内容',
  `email` varchar(255) DEFAULT NULL COMMENT '联系邮箱',
  `account_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `system_channel` tinyint(4) DEFAULT NULL COMMENT '1 苹果 2 安卓 3 PC',
  `announcement_id` bigint(20) DEFAULT NULL COMMENT '反馈回复表id',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0待处理,1处理中,2已完成,3处理失败,4已超时',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '状态更新时间',
  `delsign` tinyint(1) DEFAULT '0' COMMENT '0. 存在 1.删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`account_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_created_at` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=167 DEFAULT CHARSET=utf8mb4 COMMENT='用户反馈表'
```

