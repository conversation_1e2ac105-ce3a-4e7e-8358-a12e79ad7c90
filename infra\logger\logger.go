package logger

import (
	"fmt"
	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
	"github.com/sirupsen/logrus"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

var log = logrus.New()

// 自定义文本格式
type CustomTextFormatter struct {
	TimestampFormat string
}

func (f *CustomTextFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	// 获取调用者信息
	var caller string
	if callerField, ok := entry.Data["caller"]; ok {
		caller = fmt.Sprintf("[%s]", callerField)
	} else if entry.Caller != nil {
		// 如果没有自定义的caller字段，使用logrus提供的caller
		fileName := filepath.Base(entry.Caller.File)
		caller = fmt.Sprintf("[%s:%d]", fileName, entry.Caller.Line)
	} else {
		caller = "[unknown]"
	}

	// 格式化时间
	timestamp := entry.Time.Format(f.TimestampFormat)
	
	// 格式化日志级别 (转为大写)
	level := strings.ToUpper(fmt.Sprintf("%-5s", entry.Level.String()))
	
	// 构建日志消息
	msg := fmt.Sprintf("%s %s %s : %s\n", timestamp, level, caller, entry.Message)
	
	return []byte(msg), nil
}

const (
	// LogRetentionDays defines how many days to keep log files
	LogRetentionDays = 7
	// LogRotationHours defines how often to rotate log files
	LogRotationHours = 24
)

func InitLogger(logPath string, projectName string) error {
	// 启用调用者信息记录
	log.SetReportCaller(true)
	
	// 设置日志级别为DEBUG
	log.SetLevel(logrus.DebugLevel)
	
	// 自定义文本格式
	log.SetFormatter(&CustomTextFormatter{
		TimestampFormat: "2006-01-02 15:04:05.000",
	})
	
	// 为每个日志级别创建独立的writer
	levels := []logrus.Level{
		logrus.DebugLevel, // 添加DEBUG级别
		logrus.InfoLevel,
		logrus.WarnLevel,
		logrus.ErrorLevel,
	}

	for _, level := range levels {
		writer, err := rotatelogs.New(
			filepath.Join(logPath, fmt.Sprintf("%s-%s-%%Y%%m%%d.log", projectName, level.String())),
			rotatelogs.WithLinkName(filepath.Join(logPath, fmt.Sprintf("%s-%s.log", projectName, level.String()))),
			rotatelogs.WithMaxAge(time.Duration(LogRetentionDays)*24*time.Hour),
			rotatelogs.WithRotationTime(LogRotationHours*time.Hour),
		)
		if err != nil {
			return fmt.Errorf("config log file failed: %w", err)
		}

		log.AddHook(&writerHook{
			Writer:    writer,
			LogLevel: []logrus.Level{level},
		})
	}

	return nil
}

type writerHook struct {
	Writer    *rotatelogs.RotateLogs
	LogLevel  []logrus.Level
}

func (h *writerHook) Fire(entry *logrus.Entry) error {
	line, err := entry.String()
	if err != nil {
		return err
	}
	_, err = h.Writer.Write([]byte(line))
	return err
}

func (h *writerHook) Levels() []logrus.Level {
	return h.LogLevel
}

// Debug 添加Debug级别日志
func Debug(args ...interface{}) {
	_, file, line, ok := runtime.Caller(1)
	if ok {
		log.WithField("caller", fmt.Sprintf("%s:%d", filepath.Base(file), line)).Debug(fmt.Sprint(args...))
	} else {
		log.Debug(fmt.Sprint(args...))
	}
}

// 包装日志函数，确保调用者信息正确
func Info(args ...interface{}) {
	_, file, line, ok := runtime.Caller(1)
	if ok {
		log.WithField("caller", fmt.Sprintf("%s:%d", filepath.Base(file), line)).Info(fmt.Sprint(args...))
	} else {
		log.Info(fmt.Sprint(args...))
	}
}

func Warn(args ...interface{}) {
	_, file, line, ok := runtime.Caller(1)
	if ok {
		log.WithField("caller", fmt.Sprintf("%s:%d", filepath.Base(file), line)).Warn(fmt.Sprint(args...))
	} else {
		log.Warn(fmt.Sprint(args...))
	}
}

func Error(args ...interface{}) {
	_, file, line, ok := runtime.Caller(1)
	if ok {
		log.WithField("caller", fmt.Sprintf("%s:%d", filepath.Base(file), line)).Error(fmt.Sprint(args...))
	} else {
		log.Error(fmt.Sprint(args...))
	}
}

// SetLevel 设置日志级别
func SetLevel(level string) {
	switch strings.ToLower(level) {
	case "debug":
		log.SetLevel(logrus.DebugLevel)
	case "info":
		log.SetLevel(logrus.InfoLevel)
	case "warn":
		log.SetLevel(logrus.WarnLevel)
	case "error":
		log.SetLevel(logrus.ErrorLevel)
	default:
		log.SetLevel(logrus.InfoLevel)
	}
} 


