## 表: account_login_device

```sql
CREATE TABLE `account_login_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `token` varchar(64) DEFAULT NULL,
  `device_name` varchar(64) DEFAULT NULL,
  `system` varchar(64) DEFAULT NULL,
  `system_channel` int(11) DEFAULT NULL COMMENT '1 苹果 2 安卓 3 pc 4 鸿蒙\n',
  `model` varchar(64) DEFAULT NULL,
  `status` int(11) DEFAULT NULL COMMENT '0 登录 1 踢出',
  `device_uuid` varchar(255) DEFAULT NULL COMMENT '设备号,只允许小写字母和数字',
  `last_connection_time` bigint(20) DEFAULT NULL COMMENT '最后一次建立连接时间',
  `connection_duration` int(11) DEFAULT '0' COMMENT '连接总时长',
  `vpn_status` int(11) DEFAULT NULL COMMENT '0-断开 1-连接中',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `vpn_u` varchar(64) DEFAULT NULL,
  `device_token` varchar(256) DEFAULT NULL,
  `device_model` int(11) DEFAULT NULL COMMENT '0 小米 1 华为 2 苹果',
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `account_idx` (`account_id`,`device_uuid`,`vpn_u`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3639 DEFAULT CHARSET=utf8mb4
```

