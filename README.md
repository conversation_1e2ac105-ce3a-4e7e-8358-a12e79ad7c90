# FastBird DNS Project

FastBird DNS 是一个基于 Go 语言开发的 DNS 服务项目，主要用于管理游戏域名的黑白名单。

## 项目结构
```
fastbird-dns/
├── .cursorrules                # 项目规则文件
├── cmd/
│   └── main.go                 # 主程序入口
├── infra/                      # 基础设施代码
│   ├── base/
│   │   └── base_dao.go         # 基础数据访问对象
│   ├── logger/
│   │   └── logger.go           # 日志模块
│   ├── mysql/
│   │   └── mysql.go            # MySQL 连接管理
│   └── tool/
│       ├── ip.go               # IP 相关工具函数
│       ├── ip_test.go          # IP 工具测试
│       ├── system.go           # 系统信息工具
│       └── system_test.go      # 系统工具测试
├── internal/                   # 内部应用代码
│   ├── dao/
│   │   └── dns_dao.go          # DNS 数据访问对象
│   ├── infra/                  # 内部基础设施
│   │   ├── base/
│   │   │   └── base_dao.go     # 内部基础数据访问对象
│   │   ├── logger/
│   │   │   └── logger.go       # 内部日志模块
│   │   ├── mysql/
│   │   │   └── mysql.go        # 内部MySQL连接管理
│   │   └── tool/
│   │       ├── ip.go           # 内部IP工具
│   │       ├── ip_test.go      # 内部IP工具测试
│   │       ├── system.go       # 内部系统工具
│   │       └── system_test.go  # 内部系统工具测试
│   ├── model/
│   │   └── models.go           # 数据模型定义
│   └── service/
│       └── dns_service.go      # DNS 服务实现
├── go.mod                      # Go 模块定义
└── go.sum                      # Go 依赖校验和
```

## 功能模块

### 基础设施层 (infra)

#### MySQL 连接池
- 文件：`internal/infra/mysql/mysql.go`
- 功能：
  - 提供 MySQL 数据库连接池管理
  - 支持配置连接池参数
  - 提供全局数据库实例访问

#### 日志模块
- 文件：`internal/infra/logger/logger.go`
- 功能：
  - 支持日志文件按日期轮转
  - 不同级别日志分文件存储
  - 日志格式：`{项目名}-{日志级别}-{日期}.log`
  - 支持 Info、Warn、Error 三个日志级别

### 数据模型层 (model)
- 文件：`internal/model/models.go`
- 主要模型：
  - VpnAutoDnsNodeConf: 节点配置表
  - GameDomainBlackwhitelist: 域名黑白名单表
  - Game: 游戏信息表
  - GameRegionRelation: 游戏区服关系表

### 数据访问层 (dao)
- 文件：`internal/dao/dns_dao.go`
- 主要功能：
  - GetNodeConfByIP: 根据内网IP获取节点配置
  - GetWhitelistDomains: 获取白名单域名列表
  - GetBlacklistDomains: 获取黑名单域名列表

### 业务服务层 (service)
- 文件：`internal/service/dns_service.go`
- 主要功能：
  - GetWhitelistDomains: 获取白名单域名服务
  - GetBlacklistDomains: 获取黑名单域名服务

## 配置说明

程序支持以下命令行参数：
bash
--log-path 日志文件路径 (默认: "./logs")
--mysql-host MySQL主机地址 (默认: "localhost")
--mysql-port MySQL端口 (默认: 3306)
--mysql-user MySQL用户名 (默认: "root")
--mysql-pass MySQL密码
--mysql-db MySQL数据库名 (默认: "fastbird")

# 业务流程

### 白名单查询流程
1. 根据内网IP从 vpn_auto_dns_node_conf 表查询节点配置
2. 基于 game_region_id 和 is_foreign 查询白名单域名
3. 仅返回移动游戏(cate=2)相关的域名

### 黑名单查询流程
1. 根据内网IP获取节点配置信息
2. 基于区服筛选游戏（关联 game_region_relation 表）
3. 查询对应游戏的黑名单域名

## 运行说明

1. 编译项目：
```
go build -o fastbird-dns cmd/main.go
```

2. 运行程序：

```
 go run .\cmd\main.go
```

## 部署说明
在windows环境执行build_linux.bat，打包linux二进制包，然后发送远程执行。
# 参考实现
```
echo "Restart 参数为: $Restart"
cd /opt/v2ray_test/
if [ "$Restart" = "CN-TEST-ALL" ]; then
  servers=("************" "*************" "************" "*************" "*************" "*************" "*************") 
  for server in "${servers[@]}"; do
    echo "重启节点: $server"
    bash sshpass_cmd.sh "$server"
  done
else
  echo "只重启指定节点: $Restart"
  bash sshpass_cmd.sh "$Restart"
fi
```
## 依赖说明

- gorm.io/gorm: ORM框架
- gorm.io/driver/mysql: MySQL驱动
- github.com/lestrrat-go/file-rotatelogs: 日志轮转
- github.com/sirupsen/logrus: 日志框架

## 注意事项

1. 确保MySQL数据库中已创建所需表结构
2. 日志目录需要有写入权限
3. 程序以守护进程方式运行，每5分钟执行一次任务