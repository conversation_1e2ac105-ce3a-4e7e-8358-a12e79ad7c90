## 表: vpn_connection_close_record

```sql
CREATE TABLE `vpn_connection_close_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `device_uuid` varchar(255) DEFAULT NULL,
  `server_id` int(11) DEFAULT NULL,
  `close_type` int(11) DEFAULT NULL,
  `vpn_u` varchar(255) DEFAULT NULL,
  `vpn_s` varchar(255) DEFAULT NULL,
  `fail_reason` varchar(1024) DEFAULT NULL COMMENT '连接失败原因',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=548 DEFAULT CHARSET=utf8mb4
```

