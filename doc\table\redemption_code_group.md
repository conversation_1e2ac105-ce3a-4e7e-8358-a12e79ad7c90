## 表: redemption_code_group

```sql
CREATE TABLE `redemption_code_group` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL COMMENT '兑换码组的名称',
  `group_desc` varchar(255) NOT NULL COMMENT '组的描述',
  `business_table_name` varchar(255) DEFAULT NULL COMMENT '业务表表名',
  `start_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `membership_id` int(11) NOT NULL DEFAULT '0' COMMENT '会员等级0不限制 1手游会员 2黄金会员 3白金会员',
  `redemption_type` tinyint(4) NOT NULL DEFAULT '2' COMMENT '0不限 1付费用户邀请码 2主播推广码  3官方限时兑换码 4官方不限时码',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `extra_info` varchar(255) DEFAULT NULL COMMENT '附加信息',
  `code_count` int(11) NOT NULL DEFAULT '0' COMMENT '该组兑换码数量,0无限',
  `max_num_per_code` int(11) NOT NULL DEFAULT '0' COMMENT '每个兑换码可兑换次数，0无限',
  `reward` decimal(5,2) NOT NULL COMMENT '奖励时长',
  `delsign` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0有效1删除',
  `auto_append` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认false,只对redemption_type = 4 类型生效',
  `group_usage_limit_num` int(11) NOT NULL DEFAULT '0' COMMENT '组限制使用次数，0不限制,只对redemption_type = 4 类型生效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COMMENT='兑换码组表'
```

