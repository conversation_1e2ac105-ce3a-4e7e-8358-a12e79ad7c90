## 表: game_ip_mask_blackwhitelist_record

```sql
CREATE TABLE `game_ip_mask_blackwhitelist_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ip_mask` varchar(255) NOT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `channel` int(11) DEFAULT NULL COMMENT '1-人工添加\n2-UU加速器\n3-wireshark解析\n4-虚拟机抓取\n5-通用黑名单\n6-不黑不白\n7-vpn检测上报\n8-客户端上报\n9-v2ray抓取\n',
  `channel_status` int(11) DEFAULT NULL COMMENT '-1-不黑不白需要删除\n0-白名单\n1-黑名单',
  `resolve_domain` bigint(20) DEFAULT NULL,
  `is_foreign` int(11) DEFAULT NULL,
  `protocol` varchar(255) DEFAULT NULL,
  `game_id` bigint(20) DEFAULT NULL,
  `game_region_id` int(11) DEFAULT NULL,
  `delsign` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ip_mask` (`game_id`,`game_region_id`,`ip_mask`,`channel`,`is_foreign`,`protocol`) USING BTREE,
  KEY `ip_mask_2` (`ip_mask`)
) ENGINE=InnoDB AUTO_INCREMENT=3924203 DEFAULT CHARSET=utf8mb4 COMMENT='Ip渠道记录表'
```

